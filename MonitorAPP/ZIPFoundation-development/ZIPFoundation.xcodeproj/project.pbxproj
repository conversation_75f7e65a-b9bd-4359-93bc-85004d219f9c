// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		590A7DA626B0F8F800CBEFB4 /* Archive+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 590A7DA526B0F8F800CBEFB4 /* Archive+ZIP64.swift */; };
		590A7DA826B0F91F00CBEFB4 /* Entry+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 590A7DA726B0F91E00CBEFB4 /* Entry+ZIP64.swift */; };
		591C970D26B0FBAE00F65F97 /* ZIPFoundationEntryTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 590A7DAD26B0F95E00CBEFB4 /* ZIPFoundationEntryTests+ZIP64.swift */; };
		591C971326B0FF9F00F65F97 /* ZIPFoundationWritingTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 591C971226B0FF9F00F65F97 /* ZIPFoundationWritingTests+ZIP64.swift */; };
		591C971426B0FFAA00F65F97 /* ZIPFoundationArchiveTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 590A7DAF26B0F96800CBEFB4 /* ZIPFoundationArchiveTests+ZIP64.swift */; };
		59248AA226B6EC770078ABDA /* ZIPFoundationErrorConditionTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59248AA126B6EC770078ABDA /* ZIPFoundationErrorConditionTests+ZIP64.swift */; };
		5968EBED26D692360037EA62 /* ZIPFoundationReadingTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5968EBEC26D692360037EA62 /* ZIPFoundationReadingTests+ZIP64.swift */; };
		5968EBF126D695AD0037EA62 /* ZIPFoundationFileManagerTests+ZIP64.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5968EBF026D695AD0037EA62 /* ZIPFoundationFileManagerTests+ZIP64.swift */; };
		95B5A3D42366731B00D4D8FD /* Archive+MemoryFile.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B5A3D32366731B00D4D8FD /* Archive+MemoryFile.swift */; };
		95B5A3D72367999900D4D8FD /* ZIPFoundationMemoryTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B5A3D52367983A00D4D8FD /* ZIPFoundationMemoryTests.swift */; };
		BA0CE5042746AF0C004D8DD4 /* Archive+WritingDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA0CE5032746AF0C004D8DD4 /* Archive+WritingDeprecated.swift */; };
		BA0CE5062746AF1A004D8DD4 /* Data+CompressionDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA0CE5052746AF1A004D8DD4 /* Data+CompressionDeprecated.swift */; };
		BA0CE5082746B369004D8DD4 /* Archive+ReadingDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA0CE5072746B369004D8DD4 /* Archive+ReadingDeprecated.swift */; };
		BA0F722029756590006DD5DB /* ZIPFoundationFileAttributeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA0F721F29756590006DD5DB /* ZIPFoundationFileAttributeTests.swift */; };
		BA205D44273FCEB4001F9048 /* ZIPFoundationArchiveTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA205D43273FCEB4001F9048 /* ZIPFoundationArchiveTests.swift */; };
		BA3716AB21F0A238006E54E6 /* ZIPFoundationProgressTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA3716A921F0A1AC006E54E6 /* ZIPFoundationProgressTests.swift */; };
		BA58FB162951F49400892CE7 /* Date+ZIP.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA58FB152951F49400892CE7 /* Date+ZIP.swift */; };
		BA643D7A264811FB00018273 /* URL+ZIP.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA643D79264811FB00018273 /* URL+ZIP.swift */; };
		BA643D7C2648131C00018273 /* Archive+Progress.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA643D7B2648131C00018273 /* Archive+Progress.swift */; };
		BA85B4F2299160E4003F2748 /* Archive+Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA85B4F1299160E4003F2748 /* Archive+Deprecated.swift */; };
		BA85B4F42991614E003F2748 /* FileManager+ZIPDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = BA85B4F32991614E003F2748 /* FileManager+ZIPDeprecated.swift */; };
		BA9AFCCA2ABB645F00A1268C /* PrivacyInfo.xcprivacy in CopyFiles */ = {isa = PBXBuildFile; fileRef = BA9AFCC82ABB643E00A1268C /* PrivacyInfo.xcprivacy */; };
		BAAF13DA25CC140300070A95 /* ZIPFoundationErrorConditionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = BAAF13D925CC140300070A95 /* ZIPFoundationErrorConditionTests.swift */; };
		BACE20B826F7CE6C003BA312 /* Archive+BackingConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BACE20B726F7CE6C003BA312 /* Archive+BackingConfiguration.swift */; };
		BACE20BA26F7D18A003BA312 /* Archive+Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = BACE20B926F7D18A003BA312 /* Archive+Helpers.swift */; };
		BACE20BD26F7D545003BA312 /* Entry+Serialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = BACE20BC26F7D545003BA312 /* Entry+Serialization.swift */; };
		OBJ_33 /* ZIPFoundationDataSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_18 /* ZIPFoundationDataSerializationTests.swift */; };
		OBJ_34 /* ZIPFoundationEntryTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_19 /* ZIPFoundationEntryTests.swift */; };
		OBJ_35 /* ZIPFoundationFileManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_20 /* ZIPFoundationFileManagerTests.swift */; };
		OBJ_36 /* ZIPFoundationPerformanceTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_21 /* ZIPFoundationPerformanceTests.swift */; };
		OBJ_37 /* ZIPFoundationReadingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_22 /* ZIPFoundationReadingTests.swift */; };
		OBJ_38 /* ZIPFoundationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_23 /* ZIPFoundationTests.swift */; };
		OBJ_39 /* ZIPFoundationWritingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_24 /* ZIPFoundationWritingTests.swift */; };
		OBJ_41 /* ZIPFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = "ZIPFoundation::ZIPFoundation::Product" /* ZIPFoundation.framework */; };
		OBJ_48 /* Archive+Reading.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_9 /* Archive+Reading.swift */; };
		OBJ_49 /* Archive+Writing.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_10 /* Archive+Writing.swift */; };
		OBJ_50 /* Archive.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_11 /* Archive.swift */; };
		OBJ_51 /* Data+Compression.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_12 /* Data+Compression.swift */; };
		OBJ_52 /* Data+Serialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_13 /* Data+Serialization.swift */; };
		OBJ_53 /* Entry.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_14 /* Entry.swift */; };
		OBJ_54 /* FileManager+ZIP.swift in Sources */ = {isa = PBXBuildFile; fileRef = OBJ_15 /* FileManager+ZIP.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		BAEFCCF91EFD201600AFB154 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = OBJ_1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = "ZIPFoundation::ZIPFoundation";
			remoteInfo = ZIPFoundation;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		BA9AFCC92ABB645100A1268C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 7;
			files = (
				BA9AFCCA2ABB645F00A1268C /* PrivacyInfo.xcprivacy in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		590A7DA526B0F8F800CBEFB4 /* Archive+ZIP64.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Archive+ZIP64.swift"; sourceTree = "<group>"; };
		590A7DA726B0F91E00CBEFB4 /* Entry+ZIP64.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Entry+ZIP64.swift"; sourceTree = "<group>"; };
		590A7DAD26B0F95E00CBEFB4 /* ZIPFoundationEntryTests+ZIP64.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationEntryTests+ZIP64.swift"; sourceTree = "<group>"; };
		590A7DAF26B0F96800CBEFB4 /* ZIPFoundationArchiveTests+ZIP64.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationArchiveTests+ZIP64.swift"; sourceTree = "<group>"; };
		591C971226B0FF9F00F65F97 /* ZIPFoundationWritingTests+ZIP64.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationWritingTests+ZIP64.swift"; sourceTree = "<group>"; };
		59248AA126B6EC770078ABDA /* ZIPFoundationErrorConditionTests+ZIP64.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationErrorConditionTests+ZIP64.swift"; sourceTree = "<group>"; };
		5968EBEC26D692360037EA62 /* ZIPFoundationReadingTests+ZIP64.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationReadingTests+ZIP64.swift"; sourceTree = "<group>"; };
		5968EBF026D695AD0037EA62 /* ZIPFoundationFileManagerTests+ZIP64.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ZIPFoundationFileManagerTests+ZIP64.swift"; sourceTree = "<group>"; };
		95B5A3D32366731B00D4D8FD /* Archive+MemoryFile.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+MemoryFile.swift"; sourceTree = "<group>"; };
		95B5A3D52367983A00D4D8FD /* ZIPFoundationMemoryTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationMemoryTests.swift; sourceTree = "<group>"; };
		BA0CE5032746AF0C004D8DD4 /* Archive+WritingDeprecated.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Archive+WritingDeprecated.swift"; sourceTree = "<group>"; };
		BA0CE5052746AF1A004D8DD4 /* Data+CompressionDeprecated.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Data+CompressionDeprecated.swift"; sourceTree = "<group>"; };
		BA0CE5072746B369004D8DD4 /* Archive+ReadingDeprecated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+ReadingDeprecated.swift"; sourceTree = "<group>"; };
		BA0F721F29756590006DD5DB /* ZIPFoundationFileAttributeTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ZIPFoundationFileAttributeTests.swift; sourceTree = "<group>"; };
		BA205D43273FCEB4001F9048 /* ZIPFoundationArchiveTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationArchiveTests.swift; sourceTree = "<group>"; };
		BA3716A921F0A1AC006E54E6 /* ZIPFoundationProgressTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationProgressTests.swift; sourceTree = "<group>"; };
		BA58FB152951F49400892CE7 /* Date+ZIP.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Date+ZIP.swift"; sourceTree = "<group>"; };
		BA643D79264811FB00018273 /* URL+ZIP.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "URL+ZIP.swift"; sourceTree = "<group>"; };
		BA643D7B2648131C00018273 /* Archive+Progress.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+Progress.swift"; sourceTree = "<group>"; };
		BA85B4F1299160E4003F2748 /* Archive+Deprecated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+Deprecated.swift"; sourceTree = "<group>"; };
		BA85B4F32991614E003F2748 /* FileManager+ZIPDeprecated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "FileManager+ZIPDeprecated.swift"; sourceTree = "<group>"; };
		BA9AFCC82ABB643E00A1268C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		BAAF13D925CC140300070A95 /* ZIPFoundationErrorConditionTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationErrorConditionTests.swift; sourceTree = "<group>"; };
		BACE20B726F7CE6C003BA312 /* Archive+BackingConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+BackingConfiguration.swift"; sourceTree = "<group>"; };
		BACE20B926F7D18A003BA312 /* Archive+Helpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+Helpers.swift"; sourceTree = "<group>"; };
		BACE20BC26F7D545003BA312 /* Entry+Serialization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Entry+Serialization.swift"; sourceTree = "<group>"; };
		OBJ_10 /* Archive+Writing.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+Writing.swift"; sourceTree = "<group>"; };
		OBJ_11 /* Archive.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Archive.swift; sourceTree = "<group>"; };
		OBJ_12 /* Data+Compression.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Data+Compression.swift"; sourceTree = "<group>"; };
		OBJ_13 /* Data+Serialization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Data+Serialization.swift"; sourceTree = "<group>"; };
		OBJ_14 /* Entry.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Entry.swift; sourceTree = "<group>"; };
		OBJ_15 /* FileManager+ZIP.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "FileManager+ZIP.swift"; sourceTree = "<group>"; };
		OBJ_18 /* ZIPFoundationDataSerializationTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationDataSerializationTests.swift; sourceTree = "<group>"; };
		OBJ_19 /* ZIPFoundationEntryTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationEntryTests.swift; sourceTree = "<group>"; };
		OBJ_20 /* ZIPFoundationFileManagerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationFileManagerTests.swift; sourceTree = "<group>"; };
		OBJ_21 /* ZIPFoundationPerformanceTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationPerformanceTests.swift; sourceTree = "<group>"; };
		OBJ_22 /* ZIPFoundationReadingTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationReadingTests.swift; sourceTree = "<group>"; };
		OBJ_23 /* ZIPFoundationTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationTests.swift; sourceTree = "<group>"; };
		OBJ_24 /* ZIPFoundationWritingTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ZIPFoundationWritingTests.swift; sourceTree = "<group>"; };
		OBJ_6 /* Package.swift */ = {isa = PBXFileReference; explicitFileType = sourcecode.swift; path = Package.swift; sourceTree = "<group>"; };
		OBJ_9 /* Archive+Reading.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Archive+Reading.swift"; sourceTree = "<group>"; };
		"ZIPFoundation::ZIPFoundation::Product" /* ZIPFoundation.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ZIPFoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		"ZIPFoundation::ZIPFoundationTests::Product" /* ZIPFoundationTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; path = ZIPFoundationTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		OBJ_40 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 0;
			files = (
				OBJ_41 /* ZIPFoundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		OBJ_55 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 0;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		BA9AFCC72ABB642000A1268C /* Resources */ = {
			isa = PBXGroup;
			children = (
				BA9AFCC82ABB643E00A1268C /* PrivacyInfo.xcprivacy */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		OBJ_16 /* Tests */ = {
			isa = PBXGroup;
			children = (
				OBJ_17 /* ZIPFoundationTests */,
			);
			name = Tests;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_17 /* ZIPFoundationTests */ = {
			isa = PBXGroup;
			children = (
				BA205D43273FCEB4001F9048 /* ZIPFoundationArchiveTests.swift */,
				590A7DAF26B0F96800CBEFB4 /* ZIPFoundationArchiveTests+ZIP64.swift */,
				OBJ_18 /* ZIPFoundationDataSerializationTests.swift */,
				OBJ_19 /* ZIPFoundationEntryTests.swift */,
				590A7DAD26B0F95E00CBEFB4 /* ZIPFoundationEntryTests+ZIP64.swift */,
				BAAF13D925CC140300070A95 /* ZIPFoundationErrorConditionTests.swift */,
				59248AA126B6EC770078ABDA /* ZIPFoundationErrorConditionTests+ZIP64.swift */,
				BA0F721F29756590006DD5DB /* ZIPFoundationFileAttributeTests.swift */,
				OBJ_20 /* ZIPFoundationFileManagerTests.swift */,
				5968EBF026D695AD0037EA62 /* ZIPFoundationFileManagerTests+ZIP64.swift */,
				95B5A3D52367983A00D4D8FD /* ZIPFoundationMemoryTests.swift */,
				OBJ_21 /* ZIPFoundationPerformanceTests.swift */,
				BA3716A921F0A1AC006E54E6 /* ZIPFoundationProgressTests.swift */,
				OBJ_22 /* ZIPFoundationReadingTests.swift */,
				5968EBEC26D692360037EA62 /* ZIPFoundationReadingTests+ZIP64.swift */,
				OBJ_23 /* ZIPFoundationTests.swift */,
				OBJ_24 /* ZIPFoundationWritingTests.swift */,
				591C971226B0FF9F00F65F97 /* ZIPFoundationWritingTests+ZIP64.swift */,
			);
			name = ZIPFoundationTests;
			path = Tests/ZIPFoundationTests;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_25 /* Products */ = {
			isa = PBXGroup;
			children = (
				"ZIPFoundation::ZIPFoundationTests::Product" /* ZIPFoundationTests.xctest */,
				"ZIPFoundation::ZIPFoundation::Product" /* ZIPFoundation.framework */,
			);
			name = Products;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		OBJ_5 = {
			isa = PBXGroup;
			children = (
				OBJ_6 /* Package.swift */,
				OBJ_7 /* Sources */,
				OBJ_16 /* Tests */,
				OBJ_25 /* Products */,
			);
			sourceTree = "<group>";
		};
		OBJ_7 /* Sources */ = {
			isa = PBXGroup;
			children = (
				OBJ_8 /* ZIPFoundation */,
			);
			name = Sources;
			sourceTree = SOURCE_ROOT;
		};
		OBJ_8 /* ZIPFoundation */ = {
			isa = PBXGroup;
			children = (
				BA9AFCC72ABB642000A1268C /* Resources */,
				OBJ_11 /* Archive.swift */,
				BACE20B726F7CE6C003BA312 /* Archive+BackingConfiguration.swift */,
				BA85B4F1299160E4003F2748 /* Archive+Deprecated.swift */,
				BACE20B926F7D18A003BA312 /* Archive+Helpers.swift */,
				95B5A3D32366731B00D4D8FD /* Archive+MemoryFile.swift */,
				BA643D7B2648131C00018273 /* Archive+Progress.swift */,
				OBJ_9 /* Archive+Reading.swift */,
				BA0CE5072746B369004D8DD4 /* Archive+ReadingDeprecated.swift */,
				OBJ_10 /* Archive+Writing.swift */,
				BA0CE5032746AF0C004D8DD4 /* Archive+WritingDeprecated.swift */,
				590A7DA526B0F8F800CBEFB4 /* Archive+ZIP64.swift */,
				OBJ_12 /* Data+Compression.swift */,
				BA0CE5052746AF1A004D8DD4 /* Data+CompressionDeprecated.swift */,
				OBJ_13 /* Data+Serialization.swift */,
				BA58FB152951F49400892CE7 /* Date+ZIP.swift */,
				OBJ_14 /* Entry.swift */,
				BACE20BC26F7D545003BA312 /* Entry+Serialization.swift */,
				590A7DA726B0F91E00CBEFB4 /* Entry+ZIP64.swift */,
				OBJ_15 /* FileManager+ZIP.swift */,
				BA85B4F32991614E003F2748 /* FileManager+ZIPDeprecated.swift */,
				BA643D79264811FB00018273 /* URL+ZIP.swift */,
			);
			name = ZIPFoundation;
			path = Sources/ZIPFoundation;
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		"ZIPFoundation::ZIPFoundation" /* ZIPFoundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = OBJ_44 /* Build configuration list for PBXNativeTarget "ZIPFoundation" */;
			buildPhases = (
				OBJ_47 /* Sources */,
				OBJ_55 /* Frameworks */,
				BA9AFCC92ABB645100A1268C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ZIPFoundation;
			productName = ZIPFoundation;
			productReference = "ZIPFoundation::ZIPFoundation::Product" /* ZIPFoundation.framework */;
			productType = "com.apple.product-type.framework";
		};
		"ZIPFoundation::ZIPFoundationTests" /* ZIPFoundationTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = OBJ_29 /* Build configuration list for PBXNativeTarget "ZIPFoundationTests" */;
			buildPhases = (
				OBJ_32 /* Sources */,
				OBJ_40 /* Frameworks */,
				BAEFCCFB1EFD233800AFB154 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				OBJ_42 /* PBXTargetDependency */,
			);
			name = ZIPFoundationTests;
			productName = ZIPFoundationTests;
			productReference = "ZIPFoundation::ZIPFoundationTests::Product" /* ZIPFoundationTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		OBJ_1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				TargetAttributes = {
					"ZIPFoundation::ZIPFoundation" = {
						LastSwiftMigration = 1020;
					};
					"ZIPFoundation::ZIPFoundationTests" = {
						LastSwiftMigration = 1020;
					};
				};
			};
			buildConfigurationList = OBJ_2 /* Build configuration list for PBXProject "ZIPFoundation" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = OBJ_5;
			productRefGroup = OBJ_25 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				"ZIPFoundation::ZIPFoundation" /* ZIPFoundation */,
				"ZIPFoundation::ZIPFoundationTests" /* ZIPFoundationTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		BAEFCCFB1EFD233800AFB154 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if which swiftlint >/dev/null; then\nswiftlint\nelse\necho \"warning: SwiftLint not installed, download from https://github.com/realm/SwiftLint\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		OBJ_32 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 0;
			files = (
				591C971426B0FFAA00F65F97 /* ZIPFoundationArchiveTests+ZIP64.swift in Sources */,
				5968EBED26D692360037EA62 /* ZIPFoundationReadingTests+ZIP64.swift in Sources */,
				591C971326B0FF9F00F65F97 /* ZIPFoundationWritingTests+ZIP64.swift in Sources */,
				591C970D26B0FBAE00F65F97 /* ZIPFoundationEntryTests+ZIP64.swift in Sources */,
				5968EBF126D695AD0037EA62 /* ZIPFoundationFileManagerTests+ZIP64.swift in Sources */,
				OBJ_33 /* ZIPFoundationDataSerializationTests.swift in Sources */,
				OBJ_34 /* ZIPFoundationEntryTests.swift in Sources */,
				BAAF13DA25CC140300070A95 /* ZIPFoundationErrorConditionTests.swift in Sources */,
				BA205D44273FCEB4001F9048 /* ZIPFoundationArchiveTests.swift in Sources */,
				BA0F722029756590006DD5DB /* ZIPFoundationFileAttributeTests.swift in Sources */,
				BA3716AB21F0A238006E54E6 /* ZIPFoundationProgressTests.swift in Sources */,
				OBJ_35 /* ZIPFoundationFileManagerTests.swift in Sources */,
				OBJ_36 /* ZIPFoundationPerformanceTests.swift in Sources */,
				OBJ_37 /* ZIPFoundationReadingTests.swift in Sources */,
				95B5A3D72367999900D4D8FD /* ZIPFoundationMemoryTests.swift in Sources */,
				59248AA226B6EC770078ABDA /* ZIPFoundationErrorConditionTests+ZIP64.swift in Sources */,
				OBJ_38 /* ZIPFoundationTests.swift in Sources */,
				OBJ_39 /* ZIPFoundationWritingTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		OBJ_47 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 0;
			files = (
				OBJ_48 /* Archive+Reading.swift in Sources */,
				BA85B4F2299160E4003F2748 /* Archive+Deprecated.swift in Sources */,
				BA0CE5062746AF1A004D8DD4 /* Data+CompressionDeprecated.swift in Sources */,
				OBJ_49 /* Archive+Writing.swift in Sources */,
				OBJ_50 /* Archive.swift in Sources */,
				BACE20B826F7CE6C003BA312 /* Archive+BackingConfiguration.swift in Sources */,
				OBJ_51 /* Data+Compression.swift in Sources */,
				95B5A3D42366731B00D4D8FD /* Archive+MemoryFile.swift in Sources */,
				OBJ_52 /* Data+Serialization.swift in Sources */,
				590A7DA826B0F91F00CBEFB4 /* Entry+ZIP64.swift in Sources */,
				BACE20BA26F7D18A003BA312 /* Archive+Helpers.swift in Sources */,
				BA85B4F42991614E003F2748 /* FileManager+ZIPDeprecated.swift in Sources */,
				590A7DA626B0F8F800CBEFB4 /* Archive+ZIP64.swift in Sources */,
				BA643D7A264811FB00018273 /* URL+ZIP.swift in Sources */,
				BA58FB162951F49400892CE7 /* Date+ZIP.swift in Sources */,
				OBJ_53 /* Entry.swift in Sources */,
				BA0CE5042746AF0C004D8DD4 /* Archive+WritingDeprecated.swift in Sources */,
				OBJ_54 /* FileManager+ZIP.swift in Sources */,
				BA643D7C2648131C00018273 /* Archive+Progress.swift in Sources */,
				BA0CE5082746B369004D8DD4 /* Archive+ReadingDeprecated.swift in Sources */,
				BACE20BD26F7D545003BA312 /* Entry+Serialization.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		OBJ_42 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = "ZIPFoundation::ZIPFoundation" /* ZIPFoundation */;
			targetProxy = BAEFCCF91EFD201600AFB154 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		OBJ_3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_SWIFT_FLAGS = "-DXcode";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = "";
				SUPPORTED_PLATFORMS = "macosx iphoneos iphonesimulator appletvos appletvsimulator watchos watchsimulator xrsimulator xros";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.0;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				USE_HEADERMAP = NO;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Debug;
		};
		OBJ_30 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ZIPFoundation.xcodeproj/ZIPFoundationTests_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "@loader_path/../Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "$(inherited)";
				OTHER_SWIFT_FLAGS = "$(inherited)";
				TARGET_NAME = ZIPFoundationTests;
			};
			name = Debug;
		};
		OBJ_31 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				GCC_OPTIMIZATION_LEVEL = fast;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ZIPFoundation.xcodeproj/ZIPFoundationTests_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "@loader_path/../Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "$(inherited)";
				OTHER_SWIFT_FLAGS = "$(inherited)";
				TARGET_NAME = ZIPFoundationTests;
			};
			name = Release;
		};
		OBJ_4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				OTHER_SWIFT_FLAGS = "-DXcode";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = "";
				SUPPORTED_PLATFORMS = "macosx iphoneos iphonesimulator appletvos appletvsimulator watchos watchsimulator xrsimulator xros";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 4.0;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				USE_HEADERMAP = NO;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Release;
		};
		OBJ_45 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CURRENT_PROJECT_VERSION = 0.9.19;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_TESTABILITY = YES;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ZIPFoundation.xcodeproj/ZIPFoundation_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited)";
				"LD_RUNPATH_SEARCH_PATHS[sdk=appletvos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=iphoneos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=watchos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "$(inherited)";
				OTHER_SWIFT_FLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.peakstep.ZIPFoundation;
				PRODUCT_MODULE_NAME = "$(TARGET_NAME:c99extidentifier)";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGET_NAME = ZIPFoundation;
			};
			name = Debug;
		};
		OBJ_46 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CURRENT_PROJECT_VERSION = 0.9.19;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_TESTABILITY = YES;
				GCC_OPTIMIZATION_LEVEL = fast;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ZIPFoundation.xcodeproj/ZIPFoundation_Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited)";
				"LD_RUNPATH_SEARCH_PATHS[sdk=appletvos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=iphoneos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=watchos*]" = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "$(inherited)";
				OTHER_SWIFT_FLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.peakstep.ZIPFoundation;
				PRODUCT_MODULE_NAME = "$(TARGET_NAME:c99extidentifier)";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGET_NAME = ZIPFoundation;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		OBJ_2 /* Build configuration list for PBXProject "ZIPFoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_3 /* Debug */,
				OBJ_4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		OBJ_29 /* Build configuration list for PBXNativeTarget "ZIPFoundationTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_30 /* Debug */,
				OBJ_31 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		OBJ_44 /* Build configuration list for PBXNativeTarget "ZIPFoundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				OBJ_45 /* Debug */,
				OBJ_46 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = OBJ_1 /* Project object */;
}
