// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		9B05719A2DA4D1850007BC9B /* NetworkConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B0571992DA4D1850007BC9B /* NetworkConfig.swift */; };
		9B0DC9622C776F6B00940916 /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 9B0DC9612C776F6B00940916 /* ZIPFoundation */; };
		9B4AEE6C2D13EF2A00CD9DBA /* DatabaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B4AEE6B2D13EF2A00CD9DBA /* DatabaseView.swift */; };
		9B4AEE762D13EF6E00CD9DBA /* finalMonitor_AI_version_1_0.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B4AEE6F2D13EF6E00CD9DBA /* finalMonitor_AI_version_1_0.swift */; };
		9B4AEE782D13EF6E00CD9DBA /* D3test.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B4AEE6E2D13EF6E00CD9DBA /* D3test.swift */; };
		9B4AEE7A2D13EF6E00CD9DBA /* CalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B4AEE6D2D13EF6E00CD9DBA /* CalendarView.swift */; };
		9BAE50682C6CA69000A80BB3 /* MonitorAPPApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BAE50672C6CA69000A80BB3 /* MonitorAPPApp.swift */; };
		9BAE506A2C6CA69000A80BB3 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BAE50692C6CA69000A80BB3 /* ContentView.swift */; };
		9BAE506C2C6CA69200A80BB3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9BAE506B2C6CA69200A80BB3 /* Assets.xcassets */; };
		9BAE506F2C6CA69200A80BB3 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9BAE506E2C6CA69200A80BB3 /* Preview Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9BAE50762C6CA69200A80BB3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9BAE505C2C6CA69000A80BB3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9BAE50632C6CA69000A80BB3;
			remoteInfo = MonitorAPP;
		};
		9BAE50802C6CA69200A80BB3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9BAE505C2C6CA69000A80BB3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9BAE50632C6CA69000A80BB3;
			remoteInfo = MonitorAPP;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		9B0571992DA4D1850007BC9B /* NetworkConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkConfig.swift; sourceTree = "<group>"; };
		9B0DC95F2C77636F00940916 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		9B4AEE6B2D13EF2A00CD9DBA /* DatabaseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseView.swift; sourceTree = "<group>"; };
		9B4AEE6D2D13EF6E00CD9DBA /* CalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarView.swift; sourceTree = "<group>"; };
		9B4AEE6E2D13EF6E00CD9DBA /* D3test.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = D3test.swift; sourceTree = "<group>"; };
		9B4AEE6F2D13EF6E00CD9DBA /* finalMonitor_AI_version_1_0.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = finalMonitor_AI_version_1_0.swift; sourceTree = "<group>"; };
		9BAE50642C6CA69000A80BB3 /* MonitorAPP.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MonitorAPP.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9BAE50672C6CA69000A80BB3 /* MonitorAPPApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonitorAPPApp.swift; sourceTree = "<group>"; };
		9BAE50692C6CA69000A80BB3 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		9BAE506B2C6CA69200A80BB3 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9BAE506E2C6CA69200A80BB3 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		9BAE50702C6CA69200A80BB3 /* MonitorAPP.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MonitorAPP.entitlements; sourceTree = "<group>"; };
		9BAE50752C6CA69200A80BB3 /* MonitorAPPTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MonitorAPPTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9BAE507F2C6CA69200A80BB3 /* MonitorAPPUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MonitorAPPUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9BAE50612C6CA69000A80BB3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B0DC9622C776F6B00940916 /* ZIPFoundation in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE50722C6CA69200A80BB3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE507C2C6CA69200A80BB3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9BAE505B2C6CA69000A80BB3 = {
			isa = PBXGroup;
			children = (
				9BAE50662C6CA69000A80BB3 /* MonitorAPP */,
				9BAE50652C6CA69000A80BB3 /* Products */,
			);
			sourceTree = "<group>";
		};
		9BAE50652C6CA69000A80BB3 /* Products */ = {
			isa = PBXGroup;
			children = (
				9BAE50642C6CA69000A80BB3 /* MonitorAPP.app */,
				9BAE50752C6CA69200A80BB3 /* MonitorAPPTests.xctest */,
				9BAE507F2C6CA69200A80BB3 /* MonitorAPPUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9BAE50662C6CA69000A80BB3 /* MonitorAPP */ = {
			isa = PBXGroup;
			children = (
				9B0DC95F2C77636F00940916 /* Info.plist */,
				9BAE50672C6CA69000A80BB3 /* MonitorAPPApp.swift */,
				9B4AEE6D2D13EF6E00CD9DBA /* CalendarView.swift */,
				9B4AEE6E2D13EF6E00CD9DBA /* D3test.swift */,
				9B4AEE6F2D13EF6E00CD9DBA /* finalMonitor_AI_version_1_0.swift */,
				9B4AEE6B2D13EF2A00CD9DBA /* DatabaseView.swift */,
				9B0571992DA4D1850007BC9B /* NetworkConfig.swift */,
				9BAE50692C6CA69000A80BB3 /* ContentView.swift */,
				9BAE506B2C6CA69200A80BB3 /* Assets.xcassets */,
				9BAE50702C6CA69200A80BB3 /* MonitorAPP.entitlements */,
				9BAE506D2C6CA69200A80BB3 /* Preview Content */,
			);
			path = MonitorAPP;
			sourceTree = "<group>";
		};
		9BAE506D2C6CA69200A80BB3 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				9BAE506E2C6CA69200A80BB3 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9BAE50632C6CA69000A80BB3 /* MonitorAPP */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BAE50892C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPP" */;
			buildPhases = (
				9BAE50602C6CA69000A80BB3 /* Sources */,
				9BAE50612C6CA69000A80BB3 /* Frameworks */,
				9BAE50622C6CA69000A80BB3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MonitorAPP;
			packageProductDependencies = (
				9B0DC9612C776F6B00940916 /* ZIPFoundation */,
			);
			productName = MonitorAPP;
			productReference = 9BAE50642C6CA69000A80BB3 /* MonitorAPP.app */;
			productType = "com.apple.product-type.application";
		};
		9BAE50742C6CA69200A80BB3 /* MonitorAPPTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BAE508C2C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPPTests" */;
			buildPhases = (
				9BAE50712C6CA69200A80BB3 /* Sources */,
				9BAE50722C6CA69200A80BB3 /* Frameworks */,
				9BAE50732C6CA69200A80BB3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9BAE50772C6CA69200A80BB3 /* PBXTargetDependency */,
			);
			name = MonitorAPPTests;
			productName = MonitorAPPTests;
			productReference = 9BAE50752C6CA69200A80BB3 /* MonitorAPPTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		9BAE507E2C6CA69200A80BB3 /* MonitorAPPUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BAE508F2C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPPUITests" */;
			buildPhases = (
				9BAE507B2C6CA69200A80BB3 /* Sources */,
				9BAE507C2C6CA69200A80BB3 /* Frameworks */,
				9BAE507D2C6CA69200A80BB3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9BAE50812C6CA69200A80BB3 /* PBXTargetDependency */,
			);
			name = MonitorAPPUITests;
			productName = MonitorAPPUITests;
			productReference = 9BAE507F2C6CA69200A80BB3 /* MonitorAPPUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9BAE505C2C6CA69000A80BB3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					9BAE50632C6CA69000A80BB3 = {
						CreatedOnToolsVersion = 15.4;
					};
					9BAE50742C6CA69200A80BB3 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = 9BAE50632C6CA69000A80BB3;
					};
					9BAE507E2C6CA69200A80BB3 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = 9BAE50632C6CA69000A80BB3;
					};
				};
			};
			buildConfigurationList = 9BAE505F2C6CA69000A80BB3 /* Build configuration list for PBXProject "MonitorAPP" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 9BAE505B2C6CA69000A80BB3;
			packageReferences = (
				9B0DC9602C776F6B00940916 /* XCLocalSwiftPackageReference "../../../Downloads/ZIPFoundation-development" */,
			);
			productRefGroup = 9BAE50652C6CA69000A80BB3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9BAE50632C6CA69000A80BB3 /* MonitorAPP */,
				9BAE50742C6CA69200A80BB3 /* MonitorAPPTests */,
				9BAE507E2C6CA69200A80BB3 /* MonitorAPPUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9BAE50622C6CA69000A80BB3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9BAE506F2C6CA69200A80BB3 /* Preview Assets.xcassets in Resources */,
				9BAE506C2C6CA69200A80BB3 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE50732C6CA69200A80BB3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE507D2C6CA69200A80BB3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9BAE50602C6CA69000A80BB3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B4AEE6C2D13EF2A00CD9DBA /* DatabaseView.swift in Sources */,
				9B05719A2DA4D1850007BC9B /* NetworkConfig.swift in Sources */,
				9B4AEE762D13EF6E00CD9DBA /* finalMonitor_AI_version_1_0.swift in Sources */,
				9B4AEE782D13EF6E00CD9DBA /* D3test.swift in Sources */,
				9B4AEE7A2D13EF6E00CD9DBA /* CalendarView.swift in Sources */,
				9BAE506A2C6CA69000A80BB3 /* ContentView.swift in Sources */,
				9BAE50682C6CA69000A80BB3 /* MonitorAPPApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE50712C6CA69200A80BB3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BAE507B2C6CA69200A80BB3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9BAE50772C6CA69200A80BB3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9BAE50632C6CA69000A80BB3 /* MonitorAPP */;
			targetProxy = 9BAE50762C6CA69200A80BB3 /* PBXContainerItemProxy */;
		};
		9BAE50812C6CA69200A80BB3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9BAE50632C6CA69000A80BB3 /* MonitorAPP */;
			targetProxy = 9BAE50802C6CA69200A80BB3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		9BAE50872C6CA69200A80BB3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9BAE50882C6CA69200A80BB3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		9BAE508A2C6CA69200A80BB3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MonitorAPP/MonitorAPP.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MonitorAPP/Preview Content\"";
				DEVELOPMENT_TEAM = 8N85LY4Z45;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonitorAPP/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		9BAE508B2C6CA69200A80BB3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MonitorAPP/MonitorAPP.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MonitorAPP/Preview Content\"";
				DEVELOPMENT_TEAM = 8N85LY4Z45;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonitorAPP/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		9BAE508D2C6CA69200A80BB3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YLBEJJNR4M;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MonitorAPP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MonitorAPP";
			};
			name = Debug;
		};
		9BAE508E2C6CA69200A80BB3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YLBEJJNR4M;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MonitorAPP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MonitorAPP";
			};
			name = Release;
		};
		9BAE50902C6CA69200A80BB3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YLBEJJNR4M;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MonitorAPP;
			};
			name = Debug;
		};
		9BAE50912C6CA69200A80BB3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YLBEJJNR4M;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = shgz.MonitorAPPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MonitorAPP;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9BAE505F2C6CA69000A80BB3 /* Build configuration list for PBXProject "MonitorAPP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9BAE50872C6CA69200A80BB3 /* Debug */,
				9BAE50882C6CA69200A80BB3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BAE50892C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9BAE508A2C6CA69200A80BB3 /* Debug */,
				9BAE508B2C6CA69200A80BB3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BAE508C2C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPPTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9BAE508D2C6CA69200A80BB3 /* Debug */,
				9BAE508E2C6CA69200A80BB3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BAE508F2C6CA69200A80BB3 /* Build configuration list for PBXNativeTarget "MonitorAPPUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9BAE50902C6CA69200A80BB3 /* Debug */,
				9BAE50912C6CA69200A80BB3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		9B0DC9602C776F6B00940916 /* XCLocalSwiftPackageReference "../../../Downloads/ZIPFoundation-development" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "../../../Downloads/ZIPFoundation-development";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		9B0DC9612C776F6B00940916 /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ZIPFoundation;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 9BAE505C2C6CA69000A80BB3 /* Project object */;
}
