private func loadTasks() {
    isLoading = true
    let url = URL(string: "\(NetworkConfig.mainServer)\(NetworkConfig.Endpoints.getPendingTasks)?limit=\(itemsPerPage)&offset=\(currentPage * itemsPerPage)")!
    print("\n--- Preparing Request (loadTasks) ---")
    print("URL: \(url.absoluteString)")
    let request = URLRequest(url: url)
    print("Method: GET")
    print("Headers: \(request.allHTTPHeaderFields ?? [:])")
    print("Body: nil")
    print("--- Sending Request ---")
    URLSession.shared.dataTask(with: request) { data, response, error in
        // ... (后续处理代码不变)
    }.resume()
}

private func fetchTotalTaskCount() {
    let url = URL(string: "\(NetworkConfig.mainServer)\(NetworkConfig.Endpoints.getTotalTaskCount)")!
    print("\n--- Preparing Request (fetchTotalTaskCount) ---")
    print("URL: \(url.absoluteString)")
    let request = URLRequest(url: url)
    print("Method: GET")
    print("Headers: \(request.allHTTPHeaderFields ?? [:])")
    print("Body: nil")
    print("--- Sending Request ---")
    URLSession.shared.dataTask(with: request) { data, response, error in
        // ... (后续处理代码不变)
    }.resume()
}

private func completeTask(taskId: Int) {
    let url = URL(string: "\(NetworkConfig.mainServer)\(NetworkConfig.Endpoints.completeTask)")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    let body: [String: Any] = ["task_id": taskId, "date_completed": Date().iso8601]
    request.httpBody = try? JSONSerialization.data(withJSONObject: body)

    print("\n--- Preparing Request (completeTask) ---")
    print("URL: \(url.absoluteString)")
    print("Method: \(request.httpMethod ?? "nil")")
    print("Headers: \(request.allHTTPHeaderFields ?? [:])")
    if let bodyData = request.httpBody, let bodyString = String(data: bodyData, encoding: .utf8) {
        print("Body: \(bodyString)")
    } else {
        print("Body: nil or encoding error")
    }
    print("--- Sending Request ---")

    URLSession.shared.dataTask(with: request) { data, response, error in
        // ... (后续处理代码不变)
    }.resume()
}

private func deleteTask(taskId: Int) {
    let url = URL(string: "\(NetworkConfig.mainServer)\(NetworkConfig.Endpoints.deleteTask)")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    let body: [String: Any] = ["task_id": taskId]
    request.httpBody = try? JSONSerialization.data(withJSONObject: body)

    print("\n--- Preparing Request (deleteTask) ---")
    print("URL: \(url.absoluteString)")
    print("Method: \(request.httpMethod ?? "nil")")
    print("Headers: \(request.allHTTPHeaderFields ?? [:])")
    if let bodyData = request.httpBody, let bodyString = String(data: bodyData, encoding: .utf8) {
        print("Body: \(bodyString)")
    } else {
        print("Body: nil or encoding error")
    }
    print("--- Sending Request ---")

    URLSession.shared.dataTask(with: request) { data, response, error in
        // ... (后续处理代码不变)
    }.resume()
}

private func searchTasks() {
    guard !searchText.isEmpty else {
        loadTasks()
        return
    }
    isLoading = true
    let url = URL(string: "\(NetworkConfig.mainServer)\(NetworkConfig.Endpoints.searchTasks)?keyword=\(searchText.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")")!
    print("\n--- Preparing Request (searchTasks) ---")
    print("URL: \(url.absoluteString)")
    let request = URLRequest(url: url)
    print("Method: GET")
    print("Headers: \(request.allHTTPHeaderFields ?? [:])")
    print("Body: nil")
    print("--- Sending Request ---")
    URLSession.shared.dataTask(with: request) { data, response, error in
        // ... (后续处理代码不变)
    }.resume()
} 