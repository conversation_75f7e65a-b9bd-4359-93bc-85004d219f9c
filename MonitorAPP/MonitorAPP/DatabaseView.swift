import SwiftUI
import AppKit // 添加这一行

//全局按钮样式定义（调整字体大小和间距）
struct ToolbarButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label
                .font(.headline) // 增大字体
        }
        .padding(.horizontal, 12) // 增加水平内边距
        .padding(.vertical, 8) // 增加垂直内边距
        .background(Color.blue.opacity(configuration.isPressed ? 0.7 : 0.5))
        .foregroundColor(.white)
        .cornerRadius(10) // 增大圆角
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label
                .font(.headline) // 增大字体
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(configuration.isPressed ? 0.7 : 0.5))
        .foregroundColor(.white)
        .cornerRadius(10)
    }
}

struct AddColumnButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label
                .font(.headline) // 增大字体
        }
        .padding(.horizontal, 14)
        .padding(.vertical, 10)
        .background(Color.green.opacity(configuration.isPressed ? 0.7 : 0.5))
        .foregroundColor(.white)
        .cornerRadius(10)
    }
}



// 定义一个全局的窗口管理器，用于跟踪所有打开的窗口
class WindowManager {
    static let shared = WindowManager()
    private var windows: [NSWindowController] = []
    
    func addWindow(_ window: NSWindowController) {
        windows.append(window)
    }
    
    func removeWindow(_ window: NSWindowController) {
        windows.removeAll { $0 == window }
    }
}

// 定义数据库的结构体
struct Database: Identifiable, Equatable, Hashable {
    let id = UUID()
    let name: String
    
    static func == (lhs: Database, rhs: Database) -> Bool {
        return lhs.id == rhs.id && lhs.name == rhs.name
    }
}

struct CheckboxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        Button(action: { configuration.isOn.toggle() }) {
            Image(systemName: configuration.isOn ? "checkmark.square" : "square")
                .foregroundColor(configuration.isOn ? .blue : .gray)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



struct DatabaseView: View {
    @State private var databases: [Database] = []
    @State private var selectedDatabase: Database? = nil
    @State private var tables: [String] = []
    @State private var selectedTable: String? = nil
    @State private var isLoadingDatabases = true
    @State private var isLoadingTables = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showDeleteTableConfirmation = false

    // 备份相关的状态变量
    @State private var isBackupMode = false
    @State private var selectedTablesForBackup = Set<String>()

    // 配置项
    let host = NetworkConfig.Database.defaultHost
    let port = NetworkConfig.Database.defaultPort // 直接使用字符串类型的端口号

    // 自定义 Binding，用于全选功能
    var selectAllBinding: Binding<Bool> {
        Binding<Bool>(
            get: {
                selectedTablesForBackup.count == tables.count && !tables.isEmpty
            },
            set: { value in
                if value {
                    selectedTablesForBackup = Set(tables)
                } else {
                    selectedTablesForBackup.removeAll()
                }
            }
        )
    }

    var body: some View {
        ZStack {
            NavigationView {
                List(selection: $selectedDatabase) {
                    Section(header: HStack {
                        Text("数据库列表")
                            .font(.headline)
                        Spacer()
                        Text("共有：\(databases.count)个数据库")
                            .font(.subheadline)
                            .padding(6)
                            .background(Color.yellow.opacity(0.3))
                            .foregroundColor(.black)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.orange, lineWidth: 2)
                            )
                    }) {
                        ForEach(databases) { database in
                            Text(database.name)
                                .font(.title2) // 增大字体
                                .padding(8) // 添加内边距
                                .frame(maxWidth: .infinity, alignment: .leading) // 使文本左对齐并占满宽度
                                .background(selectedDatabase == database ? Color.blue.opacity(0.2) : Color.clear) // 选中时背景色变化
                                .cornerRadius(5)
                                .tag(database)
                        }
                    }

                    
                    
                }
                .frame(minWidth: 250, idealWidth: 300, maxWidth: 350)
                .onAppear {
                    fetchDatabases()
                }
                .onChange(of: selectedDatabase) { _ in
                    fetchTables()
                }

                
           


                VStack(alignment: .leading, spacing: 0) {
                    if let selectedDatabase = selectedDatabase {
                            // 标题区域
                        VStack(alignment: .leading, spacing: 10) {
                            HStack {
                                Text("数据库：\(selectedDatabase.name)")
                                    .font(.largeTitle)
                                    .padding([.top, .leading], 20)
                                
                                
                                
                                Text("当前选中的数据库共有：\(tables.count)个表")
                                    .font(.headline)
                                    .padding(6)
                                    .background(Color.green.opacity(0.3))
                                    .foregroundColor(.black)
                                    .cornerRadius(6)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 6)
                                            .stroke(Color.green, lineWidth: 2)
                                    )
                                    .padding([.trailing], 20)
                            }

                            Divider().padding(.vertical, 10)
                            
                            
                        }


                            Divider().padding(.vertical, 10)


                        if isLoadingTables {
                            ProgressView("正在加载表...")
                                .padding()
                        } else {
                            if isBackupMode {
                                // 备份操作栏
                                HStack {
                                    Toggle("全选", isOn: selectAllBinding)
                                    Spacer()
                                    Button(action: {
                                        isBackupMode = false
                                        selectedTablesForBackup.removeAll()
                                    }) {
                                        HStack {
                                            Image(systemName: "xmark.circle")
                                            Text("取消")
                                        }
                                    }
                                    .buttonStyle(CancelButtonStyle())

                                    Button(action: {
                                        startBackup()
                                    }) {
                                        HStack {
                                            Image(systemName: "play.circle")
                                            Text("开始")
                                        }
                                    }
                                    .buttonStyle(StartButtonStyle())
                                }
                                .padding([.horizontal, .top], 10)
                            }

                            // 表列表
                            List(selection: $selectedTable) {
                                // 修改后
                                ForEach(Array(tables.enumerated()), id: \.element) { index, table in
                                    HStack {
                                        if isBackupMode {
                                            Toggle("", isOn: Binding(
                                                get: { selectedTablesForBackup.contains(table) },
                                                set: { isSelected in
                                                    if isSelected {
                                                        selectedTablesForBackup.insert(table)
                                                    } else {
                                                        selectedTablesForBackup.remove(table)
                                                    }
                                                }
                                            ))
                                            .toggleStyle(CheckboxToggleStyle())
                                        }

                                        Text(table)
                                            .font(.title3) // 增大字体
                                            .padding(.vertical, 8) // 添加垂直内边距
                                            .onTapGesture {
                                                selectedTable = table
                                                if !isBackupMode {
                                                    openTableDataWindow(database: selectedDatabase, tableName: table)
                                                }
                                            }
                                        Spacer()

                                        if !isBackupMode {
                                            Button(action: {
                                                openTableStructureWindow(database: selectedDatabase, tableName: table)
                                            }) {
                                                Image(systemName: "square.and.pencil")
                                                    .resizable()
                                                    .frame(width: 20, height: 20) // 增大图标尺寸
                                                    .foregroundColor(.blue)
                                            }
                                            .buttonStyle(BorderlessButtonStyle())
                                        }
                                    }
                                    .background(index % 2 == 0 ? Color.gray.opacity(0.05) : Color.clear) // 增加行间隔颜色
                                    .cornerRadius(5)
                                    .padding(.horizontal, 4)
                                }

                                .onDelete(perform: deleteTable)
                            }
                        }
                    } else {
                        Text("请选择一个数据库")
                            .font(.title)
                            .padding()
                    }
                }
                .toolbar {
                    // 仅当选择了数据库且不在备份模式下时，显示相关操作按钮，适配mac平台。
                    ToolbarItemGroup(placement: .automatic) {
                        if selectedDatabase != nil && !isBackupMode {
                            Button(action: {
                                isBackupMode = true
                            }) {
                                HStack {
                                    Image(systemName: "tray.and.arrow.down")
                                    Text("备份此数据库")
                                }
                            }

                            Button(action: {
                                if let db = selectedDatabase {
                                    openCreateTableWindow(database: db)
                                }
                            }) {
                                Image(systemName: "plus")
                            }
                            .accessibilityLabel("创建新表")

                            if selectedTable != nil {
                                Button(action: {
                                    showDeleteTableConfirmation = true
                                }) {
                                    HStack {
                                        Image(systemName: "trash")
                                        Text("删除此表")
                                    }
                                }
                                .buttonStyle(ToolbarButtonStyle())
                            }
                        }
                    }

                    // 保留刷新数据库列表的按钮
                    ToolbarItem(placement: .automatic) {
                        Button(action: {
                            fetchDatabases()
                        }) {
                            Image(systemName: "arrow.clockwise")
                        }
                        .accessibilityLabel("刷新数据库列表")
                    }
                }
            }

            // 自定义弹窗：删除表确认
            if showDeleteTableConfirmation {
                CustomPopupView(
                    title: "确认删除表",
                    message: "您确定要删除此表吗？此操作不可撤销。",
                    primaryButtonTitle: "删除",
                    primaryAction: {
                        deleteSelectedTable()
                        showDeleteTableConfirmation = false
                    },
                    secondaryButtonTitle: "取消",
                    secondaryAction: {
                        showDeleteTableConfirmation = false
                    }
                )
            }

            // 自定义弹窗：错误提示
            if showAlert {
                CustomPopupView(
                    title: "错误",
                    message: alertMessage,
                    primaryButtonTitle: "确定",
                    primaryAction: {
                        showAlert = false
                    },
                    secondaryButtonTitle: nil,
                    secondaryAction: nil
                )
            }
        }
    }

    func fetchDatabases() {
        isLoadingDatabases = true
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.getDatabases) else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isLoadingDatabases = false
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            defer { isLoadingDatabases = false }
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "加载失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            guard let data = data else {
                DispatchQueue.main.async {
                    self.alertMessage = "未收到数据"
                    self.showAlert = true
                }
                return
            }

            do {
                let databaseNames = try JSONDecoder().decode([String].self, from: data)
                DispatchQueue.main.async {
                    let mappedDatabases = databaseNames.map { Database(name: $0) }
                    self.databases = mappedDatabases
                    if let firstDatabase = mappedDatabases.first {
                        self.selectedDatabase = firstDatabase
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "解码数据失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }.resume()
    }

    func fetchTables() {
        guard let selectedDatabase = selectedDatabase else { return }
        isLoadingTables = true
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.getTables + "/\(selectedDatabase.name)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isLoadingTables = false
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            defer { isLoadingTables = false }
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "加载表失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            guard let data = data else {
                DispatchQueue.main.async {
                    self.alertMessage = "未收到数据"
                    self.showAlert = true
                }
                return
            }

            do {
                let tableNames = try JSONDecoder().decode([String].self, from: data)
                DispatchQueue.main.async {
                    self.tables = tableNames
                    // 重置备份相关状态
                    self.selectedTablesForBackup.removeAll()
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "解码数据失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }.resume()
    }


    func deleteTable(at offsets: IndexSet) {
        guard let selectedDatabase = selectedDatabase else { return }
        guard let index = offsets.first else { return }
        let tableName = tables[index]

        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.deleteTable + "/\(selectedDatabase.name)/\(tableName)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "删除表失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.tables.remove(atOffsets: offsets)
            }
        }.resume()
    }

    func deleteSelectedTable() {
        guard let selectedDatabase = selectedDatabase, let selectedTable = selectedTable else { return }
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.deleteTable + "/\(selectedDatabase.name)/\(selectedTable)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "删除表失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.alertMessage = "表已成功删除"
                self.showAlert = true
                self.fetchTables()
            }
        }.resume()
    }

    // 打开创建表窗口
    func openCreateTableWindow(database: Database) {
        let contentView = CreateTableView(host: host, port: port, databaseName: database.name) {
            fetchTables()
        }

        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 600, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "创建新表")
            return
        }

        let windowWidth = screenFrame.width * 0.4
        let windowHeight = screenFrame.height * 0.6
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "创建新表")
    }

    // 打开表数据窗口
    func openTableDataWindow(database: Database, tableName: String) {
        let contentView = TableDataView(database: database, table: tableName, host: host, port: port)

        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 800, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "表：\(tableName)")
            return
        }

        let windowFrame = screenFrame

        createWindow(frame: windowFrame, contentView: contentView, title: "表：\(tableName)")
    }

    // 打开表结构窗口
    func openTableStructureWindow(database: Database, tableName: String) {
        let contentView = TableStructureView(database: database, table: tableName, host: host, port: port)

        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 600, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "表结构：\(tableName)")
            return
        }

        let windowWidth = screenFrame.width * 0.5
        let windowHeight = screenFrame.height * 0.6
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "表结构：\(tableName)")
    }

    private func createWindow<Content: View>(frame: NSRect, contentView: Content, title: String) {
        let window = NSWindow(
            contentRect: frame,
            styleMask: [.titled, .closable, .resizable, .miniaturizable],
            backing: .buffered, defer: false)
        window.title = title
        window.contentView = NSHostingView(rootView: contentView)
        window.center()

        window.setFrame(frame, display: true)
        window.makeKeyAndOrderFront(nil)

        let windowController = NSWindowController(window: window)
        WindowManager.shared.addWindow(windowController)
    }

    // 开始备份
    func startBackup() {
        guard let selectedDatabase = selectedDatabase else { return }

        let selectedTables = Array(selectedTablesForBackup)
        let contentView = DatabaseBackupView(databaseName: selectedDatabase.name, tables: selectedTables, host: host, port: port)

        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 500, height: 300)
            createWindow(frame: defaultFrame, contentView: contentView, title: "数据库备份")
            return
        }

        let windowWidth = screenFrame.width * 0.5
        let windowHeight = screenFrame.height * 0.4
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "数据库备份")

        isBackupMode = false
        selectedTablesForBackup.removeAll()
    }
}



// 更新后的 DatabaseBackupView
struct DatabaseBackupView: View {
    let databaseName: String
    let tables: [String]
    let host: String
    let port: String  // 修改为字符串类型

    @State private var progress: Double = 0.0
    @State private var totalBytesWritten: Int64 = 0
    @State private var totalBytesExpectedToWrite: Int64 = NSURLSessionTransferSizeUnknown
    @State private var isDownloading = false
    @State private var statusMessage = "准备开始备份..."
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var backupFilePath: String = ""
    @State private var fileName: String = ""
    @State private var fileSize: Int64 = 0

    @State private var downloadTask: URLSessionDownloadTask?
    @State private var isCancelled = false

    var body: some View {
        VStack(spacing: 15) {
            HStack {
                // 用文字加上样式替代原本的database图标
                Text("DB")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(5)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(5)
                Text("备份数据库：\(databaseName)")
                    .font(.headline)
                    .padding()
            }

            Divider()
                .padding(.vertical)

            if !tables.isEmpty {
                Text("备份的表：")
                    .font(.subheadline)
                ScrollView {
                    VStack(alignment: .leading, spacing: 5) {
                        ForEach(tables, id: \.self) { table in
                            HStack {
                                Image(systemName: "table")
                                    .resizable()
                                    .frame(width: 20, height: 20)
                                Text(table)
                            }
                        }
                    }
                }
                .frame(maxHeight: 200)
            } else {
                Text("备份整个数据库")
                    .font(.subheadline)
            }

            Divider()
                .padding(.vertical)

            if isDownloading {
                if !fileName.isEmpty {
                    Text("文件名：\(fileName)")
                }
                if totalBytesExpectedToWrite > 0 {
                    Text(String(format: "文件大小：%.2f MB", Double(totalBytesExpectedToWrite) / (1024 * 1024)))
                }
                Text(String(format: "已下载：%.2f MB", Double(totalBytesWritten) / (1024 * 1024)))

                ProgressView(value: progress)
                    .padding()

                Button(action: {
                    isCancelled = true
                    downloadTask?.cancel()
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                        Text("取消下载")
                    }
                }
                .buttonStyle(CancelButtonStyle())
            } else if progress == 1.0 {
                Text("备份完成")
                    .font(.title)
                    .foregroundColor(.green)
                    .padding()

                HStack {
                    Text("备份文件路径：")
                    TextField("", text: $backupFilePath)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .disabled(true)
                    Button("在访达中显示") {
                        NSWorkspace.shared.activateFileViewerSelecting([URL(fileURLWithPath: backupFilePath)])
                    }
                }
                .padding()
            }

            if !isDownloading && progress < 1.0 && !isCancelled {
                HStack(spacing: 20) {
                    Button(action: {
                        startBackupAndDownload()
                    }) {
                        HStack {
                            Image(systemName: "play.circle")
                            Text("开始备份并下载")
                        }
                    }
                    .buttonStyle(StartButtonStyle())

                    Button(action: {
                        closeWindow()
                    }) {
                        HStack {
                            Image(systemName: "xmark.circle")
                            Text("取消")
                        }
                    }
                    .buttonStyle(CancelButtonStyle())
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("错误"),
                message: Text(alertMessage),
                primaryButton: .default(Text("重试")) {
                    startBackupAndDownload()
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .frame(minWidth: 500, minHeight: 400)
        .onDisappear {
            downloadTask?.cancel()
        }
    }

    struct StartButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            configuration.label
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(configuration.isPressed ? Color.green.opacity(0.7) : Color.green)
                .foregroundColor(.white)
                .cornerRadius(8)
        }
    }

    struct CancelButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            configuration.label
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(configuration.isPressed ? Color.red.opacity(0.7) : Color.red)
                .foregroundColor(.white)
                .cornerRadius(8)
        }
    }

    func startBackupAndDownload() {
        progress = 0.0
        totalBytesWritten = 0
        totalBytesExpectedToWrite = NSURLSessionTransferSizeUnknown
        fileName = ""
        fileSize = 0
        backupFilePath = ""
        showAlert = false
        alertMessage = ""
        isDownloading = true
        isCancelled = false
        statusMessage = "正在下载备份..."

        guard var urlComponents = URLComponents(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.downloadBackup) else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isDownloading = false
            return
        }
        urlComponents.queryItems = [
            URLQueryItem(name: "database", value: databaseName)
        ]
        for table in tables {
            urlComponents.queryItems?.append(URLQueryItem(name: "tables", value: table))
        }

        guard let url = urlComponents.url else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isDownloading = false
            return
        }

        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForResource = 3600
        let session = URLSession(configuration: configuration, delegate: DownloadDelegate(databaseName: databaseName, progressHandler: { progressValue, totalBytesWritten, totalBytesExpectedToWrite, fileName, fileSize in
            DispatchQueue.main.async {
                self.totalBytesWritten = totalBytesWritten
                self.totalBytesExpectedToWrite = totalBytesExpectedToWrite
                self.fileName = fileName ?? ""
                self.fileSize = fileSize ?? 0
                if totalBytesExpectedToWrite > 0 {
                    self.progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)
                } else {
                    self.progress = 0.0
                }
            }
        }, completionHandler: { destinationURL in
            DispatchQueue.main.async {
                self.statusMessage = "备份文件已保存至："
                self.backupFilePath = destinationURL.path
                self.isDownloading = false
                self.progress = 1.0
                if let attributes = try? FileManager.default.attributesOfItem(atPath: destinationURL.path),
                   let actualFileSize = attributes[.size] as? Int64 {
                    self.totalBytesExpectedToWrite = actualFileSize
                }
            }
        }, errorHandler: { error in
            DispatchQueue.main.async {
                self.alertMessage = error.localizedDescription
                self.showAlert = true
                self.isDownloading = false
                self.progress = 0.0
            }
        }), delegateQueue: nil)

        downloadTask = session.downloadTask(with: url)
        downloadTask?.resume()
    }

    func closeWindow() {
        NSApplication.shared.keyWindow?.close()
    }
}




class DownloadDelegate: NSObject, URLSessionDownloadDelegate {
    var totalBytesWritten: Int64 = 0
    var totalBytesExpectedToWrite: Int64 = NSURLSessionTransferSizeUnknown
    var progress: Double = 0.0
    var fileName: String?
    var fileSize: Int64?

    let progressHandler: (Double, Int64, Int64, String?, Int64?) -> Void
    let completionHandler: (URL) -> Void
    let errorHandler: (Error) -> Void
    let databaseName: String

    init(databaseName: String, progressHandler: @escaping (Double, Int64, Int64, String?, Int64?) -> Void, completionHandler: @escaping (URL) -> Void, errorHandler: @escaping (Error) -> Void) {
        self.databaseName = databaseName
        self.progressHandler = progressHandler
        self.completionHandler = completionHandler
        self.errorHandler = errorHandler
    }

    // 获取响应头中的 Content-Length 和文件名
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        if let httpResponse = response as? HTTPURLResponse {
            let statusCode = httpResponse.statusCode
            if statusCode != 200 {
                var errorMessage = "服务器返回错误状态码：\(statusCode)"
                if let data = httpResponse.value(forKey: "data") as? Data,
                   let errorResponse = try? JSONDecoder().decode([String: String].self, from: data),
                   let serverErrorMessage = errorResponse["error"] {
                    errorMessage += "\n\(serverErrorMessage)"
                }
                let error = NSError(domain: "HTTPError", code: statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                self.errorHandler(error)
                completionHandler(.cancel)
                return
            }
            if let contentLength = httpResponse.allHeaderFields["Content-Length"] as? String, let contentLengthValue = Int64(contentLength) {
                self.totalBytesExpectedToWrite = contentLengthValue
                self.fileSize = contentLengthValue
            } else if httpResponse.expectedContentLength != NSURLSessionTransferSizeUnknown {
                self.totalBytesExpectedToWrite = httpResponse.expectedContentLength
                self.fileSize = httpResponse.expectedContentLength
            }
            if let contentDisposition = httpResponse.allHeaderFields["Content-Disposition"] as? String {
                self.fileName = extractFileName(from: contentDisposition)
            }
        }
        completionHandler(.allow)
    }

    func extractFileName(from contentDisposition: String) -> String? {
        let pattern = "filename\\*=UTF-8''(.+)"
        if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
            if let match = regex.firstMatch(in: contentDisposition, options: [], range: NSRange(location: 0, length: contentDisposition.utf16.count)) {
                if let range = Range(match.range(at: 1), in: contentDisposition) {
                    let fileName = String(contentDisposition[range])
                    return fileName.removingPercentEncoding
                }
            }
        }
        return nil
    }

    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64,
                    totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
        self.totalBytesWritten = totalBytesWritten

        // 如果 totalBytesExpectedToWrite 未知，尝试更新
        if self.totalBytesExpectedToWrite == NSURLSessionTransferSizeUnknown && totalBytesExpectedToWrite != NSURLSessionTransferSizeUnknown {
            self.totalBytesExpectedToWrite = totalBytesExpectedToWrite
            self.fileSize = totalBytesExpectedToWrite
        }

        if self.totalBytesExpectedToWrite > 0 {
            self.progress = Double(totalBytesWritten) / Double(self.totalBytesExpectedToWrite)
        } else {
            self.progress = 0.0
        }

        DispatchQueue.main.async {
            self.progressHandler(self.progress, self.totalBytesWritten, self.totalBytesExpectedToWrite, self.fileName, self.fileSize)
        }
    }

    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        // 检查下载文件大小是否为 0
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: location.path)
            if let fileSize = attributes[.size] as? NSNumber, fileSize.int64Value == 0 {
                let error = NSError(domain: "DownloadError", code: -1, userInfo: [NSLocalizedDescriptionKey: "下载的文件大小为 0KB，请重试。"])
                self.errorHandler(error)
                return
            }
        } catch {
            self.errorHandler(error)
            return
        }

        // 在此立即将文件移动到持久位置
        let fileManager = FileManager.default
        do {
            // 获取下载目录
            let downloadsDirectory = try fileManager.url(for: .downloadsDirectory, in: .userDomainMask, appropriateFor: nil, create: false)
            let destinationURL: URL
            if let fileName = self.fileName {
                destinationURL = downloadsDirectory.appendingPathComponent(fileName)
            } else {
                destinationURL = downloadsDirectory.appendingPathComponent("\(databaseName)_backup_\(Date().timeIntervalSince1970).sql")
            }

            // 如果文件已存在，添加后缀避免覆盖
            var finalURL = destinationURL
            var counter = 1
            while fileManager.fileExists(atPath: finalURL.path) {
                finalURL = destinationURL.deletingPathExtension()
                    .appendingPathExtension("(\(counter))")
                    .appendingPathExtension(destinationURL.pathExtension)
                counter += 1
            }

            // 尝试移动文件到持久位置
            try fileManager.moveItem(at: location, to: finalURL)

            // 在主线程中通知完成，并传递目标文件的URL
            DispatchQueue.main.async {
                self.completionHandler(finalURL)
            }
        } catch {
            DispatchQueue.main.async {
                self.errorHandler(error)
            }
        }
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            DispatchQueue.main.async {
                self.errorHandler(error)
            }
        }
    }
}





// 其余的代码保持不变，包括其他视图和结构体，如 CreateTableView、TableDataView 等。

// 请确保在您的项目中包含所有相关的视图和结构体。

// 以下为其他视图和结构体的代码...

// 请将其余的代码保持不变



// 定义列的结构体
struct Column: Identifiable, Codable {
    let id = UUID()
    var name: String = ""
    var type: String = "int"
    var isNullable: Bool = true
    var isPrimaryKey: Bool = false
    var isAutoIncrement: Bool = false
    var isEditable: Bool = true
    var action: String? = nil  // 'add', 'modify', 'drop'

    enum CodingKeys: String, CodingKey {
        case name
        case type
        case isNullable
        case isPrimaryKey
        case isAutoIncrement
        case action
    }

    // 自定义初始化方法
    init(name: String = "", type: String = "int", isNullable: Bool = true, isPrimaryKey: Bool = false, isAutoIncrement: Bool = false, action: String? = nil) {
        self.name = name
        self.type = type
        self.isNullable = isNullable
        self.isPrimaryKey = isPrimaryKey
        self.isAutoIncrement = isAutoIncrement
        self.isEditable = self.name.lowercased() != "id"
        self.action = action
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.name = try container.decodeIfPresent(String.self, forKey: .name) ?? ""
        self.type = try container.decodeIfPresent(String.self, forKey: .type) ?? "int"
        self.isNullable = try container.decodeIfPresent(Bool.self, forKey: .isNullable) ?? true
        self.isPrimaryKey = try container.decodeIfPresent(Bool.self, forKey: .isPrimaryKey) ?? false
        self.isAutoIncrement = try container.decodeIfPresent(Bool.self, forKey: .isAutoIncrement) ?? false
        self.action = try container.decodeIfPresent(String.self, forKey: .action)

        // 根据列名设置 isEditable
        self.isEditable = self.name.lowercased() != "id"
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encode(isNullable, forKey: .isNullable)
        try container.encode(isPrimaryKey, forKey: .isPrimaryKey)
        try container.encode(isAutoIncrement, forKey: .isAutoIncrement)
        try container.encode(action, forKey: .action)
    }
}

struct CreateTableView: View {
    let host: String
    let port: String  // 修改为字符串类型
    let databaseName: String
    var onComplete: () -> Void

    @State private var tableName: String = ""
    @State private var columns: [Column] = [
        Column(name: "id", type: "int", isPrimaryKey: true, isAutoIncrement: true)
    ]
    @State private var showAlert = false
    @State private var alertMessage = ""

    var body: some View {
        VStack {
            // 标题区域
            HStack {
                Image(systemName: "plus.rectangle.on.folder")
                    .resizable()
                    .frame(width: 40, height: 40)
                    .foregroundColor(.green)
                Text("创建新表")
                    .font(.title)
                    .padding(.leading, 10)
                Spacer()
            }
            .padding()

            Divider()

            // 表名输入
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "rectangle.and.pencil.and.ellipsis")
                        .foregroundColor(.gray)
                    Text("表名")
                        .font(.headline)
                }
                TextField("请输入表名", text: $tableName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            .padding()

            Divider()

            // 列输入
            ScrollView {
                VStack(spacing: 15) {
                    ForEach($columns) { $column in
                        ColumnInputView(column: $column)
                    }
                    Button(action: addColumn) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.green)
                            Text("添加列")
                        }
                    }
                    .buttonStyle(AddColumnButtonStyle())
                    .padding()
                }
                .padding()
            }

            Spacer()
        }
        .navigationTitle("创建新表")
        .toolbar {
            ToolbarItemGroup(placement: .confirmationAction) {
                Button(action: createTable) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                        Text("保存")
                    }
                }
                .buttonStyle(ToolbarButtonStyle())
            }
            ToolbarItemGroup(placement: .cancellationAction) {
                Button(action: {
                    closeWindow()
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                        Text("取消")
                    }
                }
                .buttonStyle(SecondaryButtonStyle())
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定"), action: {
                if alertMessage == "表创建成功" {
                    closeWindow()
                    onComplete() // 在表创建成功后调用刷新
                }
            }))
        }
        .frame(minWidth: 500, minHeight: 600)
    }

    // 自定义按钮样式
    struct AddColumnButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            configuration.label
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.green.opacity(configuration.isPressed ? 0.7 : 0.5))
                .foregroundColor(.white)
                .cornerRadius(8)
        }
    }



    func addColumn() {
        columns.append(Column())
    }

    func createTable() {
        // 检查表名是否为空
        if tableName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            self.alertMessage = "表名不能为空"
            self.showAlert = true
            return
        }

        // 检查是否有重复的 'id' 列
        for column in columns {
            // 校验列名是否为空
            if column.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                self.alertMessage = "列名不能为空"
                self.showAlert = true
                return
            }

            if column.name.lowercased() == "id" && column.isEditable {
                self.alertMessage = "不能添加名为 'id' 的列"
                self.showAlert = true
                return
            }
            if column.name.lowercased() != "id" && (column.isPrimaryKey || column.isAutoIncrement) {
                self.alertMessage = "除 'id' 列外，不能设置主键或自动增长"
                self.showAlert = true
                return
            }
        }

        let parameters: [String: Any] = [
            "tableName": tableName,
            "charset": "utf8mb4",
            "collation": "utf8mb4_general_ci",
            "columns": columns.map { [
                "name": $0.name,
                "type": $0.type,
                "isPrimaryKey": $0.isPrimaryKey,
                "isAutoIncrement": $0.isAutoIncrement,
                "isNullable": !$0.isPrimaryKey,
            ] }
        ]

        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.createTable + "/\(databaseName)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters, options: [])
        } catch {
            self.alertMessage = "参数序列化失败"
            self.showAlert = true
            return
        }

        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "请求失败：\(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    DispatchQueue.main.async {
                        self.alertMessage = "表创建成功"
                        self.showAlert = true
                    }
                } else {
                    if let data = data,
                       let errorResponse = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let errorMessage = errorResponse["error"] as? String {
                        DispatchQueue.main.async {
                            self.alertMessage = "创建表失败：\(errorMessage)"
                            self.showAlert = true
                        }
                    } else {
                        DispatchQueue.main.async {
                            self.alertMessage = "创建表失败：未知错误"
                            self.showAlert = true
                        }
                    }
                }
            }
        }.resume()
    }

    func closeWindow() {
        NSApplication.shared.keyWindow?.close()
    }
}

struct TableStructureView: View {
    let database: Database
    let table: String
    let host: String
    let port: String  // 修改为字符串类型

    @State private var columns: [Column] = []
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var isLoading = true
    @State private var showDeleteColumnConfirmation = false
    @State private var columnToDeleteIndex: Int? = nil

    var body: some View {
        ZStack {
            VStack {
                // 标题区域
                HStack {
                    Image(systemName: "tablecells.badge.ellipsis")
                        .resizable()
                        .frame(width: 40, height: 40)
                        .foregroundColor(.blue)
                    Text("表结构：\(table)")
                        .font(.title)
                        .padding(.leading, 10)
                    Spacer()
                }
                .padding()

                Divider()

                if isLoading {
                    ProgressView("正在加载表结构...")
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                        .scaleEffect(1.5)
                        .padding()
                } else {
                    List {
                        ForEach(Array(columns.enumerated()), id: \.offset) { (index, column) in
                            HStack {
                                ColumnInputView(column: Binding(
                                    get: { self.columns[index] },
                                    set: { self.columns[index] = $0 }
                                ))
                                Spacer()
                                // 添加删除字段按钮
                                if column.name.lowercased() != "id" {
                                    Button(action: {
                                        columnToDeleteIndex = index
                                        showDeleteColumnConfirmation = true
                                    }) {
                                        Image(systemName: "trash")
                                            .foregroundColor(.red)
                                    }
                                    .buttonStyle(BorderlessButtonStyle())
                                }
                            }
                        }
                    }
                    .listStyle(PlainListStyle())
                }

                Spacer()
            }
            .frame(minWidth: 500, minHeight: 600)
            .toolbar {
                ToolbarItemGroup(placement: .navigation) {
                    Button(action: {
                        fetchTableSchema()
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("刷新")
                        }
                    }
                    .buttonStyle(ToolbarButtonStyle())

                    Button(action: {
                        addColumn()
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("添加列")
                        }
                    }
                    .buttonStyle(ToolbarButtonStyle())

                    Button(action: {
                        alterTable()
                    }) {
                        HStack {
                            Image(systemName: "pencil")
                            Text("保存更改")
                        }
                    }
                    .buttonStyle(ToolbarButtonStyle())

                    Button(action: {
                        closeWindow()
                    }) {
                        HStack {
                            Image(systemName: "xmark.circle")
                            Text("关闭")
                        }
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
            }
            .onAppear {
                fetchTableSchema()
            }

            // 自定义弹窗：删除字段确认
            if showDeleteColumnConfirmation {
                CustomPopupView(
                    title: "确认删除字段",
                    message: "您确定要删除这个字段吗？此操作不可撤销。",
                    primaryButtonTitle: "删除",
                    primaryAction: {
                        if let index = columnToDeleteIndex {
                            columns[index].action = "drop"
                            alterTable()
                            columnToDeleteIndex = nil
                        }
                        showDeleteColumnConfirmation = false
                    },
                    secondaryButtonTitle: "取消",
                    secondaryAction: {
                        columnToDeleteIndex = nil
                        showDeleteColumnConfirmation = false
                    }
                )
            }

            // 自定义弹窗：提示信息
            if showAlert {
                CustomPopupView(
                    title: "提示",
                    message: alertMessage,
                    primaryButtonTitle: "确定",
                    primaryAction: {
                        showAlert = false
                    },
                    secondaryButtonTitle: nil,
                    secondaryAction: nil
                )
            }
        }
    }

    func fetchTableSchema() {
        isLoading = true
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.getTableSchema + "/\(database.name)/\(table)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isLoading = false
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            defer { isLoading = false }
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "加载表结构失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            guard let data = data else {
                DispatchQueue.main.async {
                    self.alertMessage = "未收到数据"
                    self.showAlert = true
                }
                return
            }

            do {
                let decoder = JSONDecoder()
                let columnsInfo = try decoder.decode([Column].self, from: data)
                DispatchQueue.main.async {
                    self.columns = columnsInfo
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "解码数据失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }.resume()
    }

    func addColumn() {
        columns.append(Column(action: "add"))
    }

    func alterTable() {
        let alterations = columns.compactMap { column -> [String: Any]? in
            guard let action = column.action else { return nil }
            return [
                "action": action,
                "column": [
                    "name": column.name,
                    "type": column.type,
                    "isPrimaryKey": column.isPrimaryKey,
                    "isAutoIncrement": column.isAutoIncrement,
                    "isNullable": column.isNullable
                ]
            ]
        }

        if alterations.isEmpty {
            self.alertMessage = "没有任何更改需要保存"
            self.showAlert = true
            return
        }

        let parameters: [String: Any] = [
            "alterations": alterations,
            "columns": columns.map { column in
                [
                    "name": column.name,
                    "isPrimaryKey": column.isPrimaryKey
                ]
            }
        ]

        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.alterTable + "/\(database.name)/\(table)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "PUT"

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters, options: [])
        } catch {
            self.alertMessage = "参数序列化失败"
            self.showAlert = true
            return
        }

        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "请求失败：\(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.alertMessage = "表结构已更新"
                self.showAlert = true
                fetchTableSchema()
            }
        }.resume()
    }

    func closeWindow() {
        NSApplication.shared.keyWindow?.close()
    }
}


// 自定义弹窗视图
struct CustomPopupView: View {
    let title: String
    let message: String
    let primaryButtonTitle: String
    let primaryAction: () -> Void
    let secondaryButtonTitle: String?
    let secondaryAction: (() -> Void)?

    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 20) {
                Text(title)
                    .font(.headline)
                Text(message)
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .padding()

                HStack {
                    if let secondaryButtonTitle = secondaryButtonTitle, let secondaryAction = secondaryAction {
                        Button(secondaryButtonTitle) {
                            secondaryAction()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }

                    Button(primaryButtonTitle) {
                        primaryAction()
                    }
                    .buttonStyle(ToolbarButtonStyle())
                }
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            .cornerRadius(10)
            .shadow(radius: 10)
            .frame(maxWidth: 300)
        }
    }
}

struct ColumnInputView: View {
    @Binding var column: Column
    
    // 定义允许的字段类型
    let allowedTypes = ["int", "text", "longblob", "longtext", "tinytext"]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) { // 增加垂直间距
            HStack(alignment: .center, spacing: 15) {
                Text("列名:")
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .frame(width: 80, alignment: .leading)
                TextField("请输入列名", text: $column.name)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(!column.isEditable)
                    .padding(.vertical, 5)
            }
            
            HStack(alignment: .center, spacing: 15) {
                Text("数据类型:")
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .frame(width: 80, alignment: .leading)
                
                CustomSegmentedControl(
                    options: allowedTypes,
                    selection: $column.type,
                    selectedColor: .orange, // 设置高亮颜色为橙色
                    unselectedColor: .gray.opacity(0.3),
                    selectedTextColor: .white,
                    unselectedTextColor: .primary
                )
                .disabled(!column.isEditable)
                .padding(.vertical, 5)
            }
            
            // 显示不符合类型的字段，并同时启用文本选择和上下文菜单
            if !allowedTypes.contains(column.type.lowercased()) {
                Text("原本类型: \(column.type)")
                    .foregroundColor(.red)
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.red, lineWidth: 1)
                    )
                    .padding(.top, -10)
                    .textSelection(.enabled) // 允许文本选择和复制
                    .contextMenu {
                        Button(action: {
                            // 复制文本到剪贴板
                            NSPasteboard.general.clearContents()
                            NSPasteboard.general.setString(column.type, forType: .string)
                        }) {
                            Text("复制")
                            Image(systemName: "doc.on.doc")
                        }
                    }
            }
            
            Toggle("允许为空", isOn: $column.isNullable)
                .disabled(!column.isEditable)
                .padding(.leading, 80) // 对齐 Toggle
                .foregroundColor(.primary)
            
            if column.name.lowercased() == "id" {
                Toggle("主键", isOn: $column.isPrimaryKey)
                    .disabled(true)
                    .padding(.leading, 80) // 对齐 Toggle
                    .foregroundColor(.primary)
                Toggle("自动增长", isOn: $column.isAutoIncrement)
                    .disabled(true)
                    .padding(.leading, 80) // 对齐 Toggle
                    .foregroundColor(.primary)
            }
        }
        .padding()
        .background(Color(NSColor.windowBackgroundColor).opacity(0.9))
        .cornerRadius(12)
        .shadow(color: Color.gray.opacity(0.2), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
        .onChange(of: column.type) { newType in
            if allowedTypes.contains(newType.lowercased()) {
                // 类型匹配时，执行相关逻辑（如颜色高亮）
            }
        }
    }
}

struct CustomSegmentedControl: View {
    var options: [String]
    @Binding var selection: String
    
    // 自定义颜色
    var selectedColor: Color = .orange
    var unselectedColor: Color = .gray.opacity(0.3)
    var selectedTextColor: Color = .white
    var unselectedTextColor: Color = .primary
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(options, id: \.self) { option in
                Button(action: {
                    withAnimation {
                        selection = option
                    }
                }) {
                    Text(option.capitalized)
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(selection == option ? selectedColor : unselectedColor)
                        .foregroundColor(selection == option ? selectedTextColor : unselectedTextColor)
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(unselectedColor, lineWidth: 1)
        )
    }
}


struct TableRow: Identifiable {
    let id = UUID()
    var data: [String: String]
}

struct DynamicTableView: View {
    let data: [TableRow]
    let columns: [String]
    let openEditRowWindow: (Dictionary<String, String>) -> Void
    let deleteRow: (Dictionary<String, String>) -> Void

    var body: some View {
        ScrollView([.horizontal, .vertical]) {
            VStack(spacing: 0) {
                // 表头
                HStack(spacing: 0) {
                    ForEach(columns, id: \.self) { column in
                        Text(column)
                            .font(.headline)
                            .frame(width: 150, alignment: .leading)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 4)
                            .lineLimit(1)
                            .truncationMode(.tail)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.primary)
                    }
                    Text("操作")
                        .font(.headline)
                        .frame(width: 100, alignment: .leading)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 4)
                        .lineLimit(1)
                        .truncationMode(.tail)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.primary)
                }
                .background(Color.blue.opacity(0.2))

                // 数据行
                ForEach(data.indices, id: \.self) { index in
                    let row = data[index]
                    HStack(spacing: 0) {
                        ForEach(columns, id: \.self) { column in
                            Text(row.data[column] ?? "")
                                .frame(width: 150, alignment: .leading)
                                .padding(.vertical, 4)
                                .padding(.horizontal, 4)
                                .lineLimit(1)
                                .truncationMode(.tail)
                                .help(row.data[column] ?? "") // 鼠标悬停显示完整内容
                                .background(index % 2 == 0 ? Color.gray.opacity(0.1) : Color.clear)
                        }
                        HStack {
                            Button(action: {
                                openEditRowWindow(row.data)
                            }) {
                                Image(systemName: "pencil")
                            }
                            .buttonStyle(BorderlessButtonStyle())
                            Button(action: {
                                deleteRow(row.data)
                            }) {
                                Image(systemName: "trash")
                            }
                            .buttonStyle(BorderlessButtonStyle())
                        }
                        .frame(width: 100)
                        .padding(.vertical, 4)
                        .padding(.horizontal, 4)
                        .background(index % 2 == 0 ? Color.gray.opacity(0.1) : Color.clear)
                    }
                    .background(index % 2 == 0 ? Color.gray.opacity(0.1) : Color.clear)
                    .foregroundColor(.primary)
                    .border(Color.gray.opacity(0.5), width: 0.5)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct TableDataView: View {
    let database: Database
    let table: String
    let host: String
    let port: String  // 修改为字符串类型

    @State private var data: [TableRow] = []
    @State private var columns: [String] = []
    @State private var isLoading = true
    @State private var showAlert = false
    @State private var alertMessage = ""

    @State private var currentPage = 1
    @State private var totalRecords = 0
    @State private var pageNumberInput: String = ""
    let recordsPerPage = 100

    var totalPages: Int {
        return (totalRecords + recordsPerPage - 1) / recordsPerPage
    }

    var body: some View {
        VStack {
            // 标题区域
            HStack {
                Image(systemName: "table")
                    .resizable()
                    .frame(width: 40, height: 40)
                    .foregroundColor(.blue)
                Text("表：\(table)")
                    .font(.title)
                    .padding(.leading, 10)
                Spacer()
            }
            .padding()

            Divider()

            if isLoading {
                ProgressView("正在加载数据...")
                    .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                    .scaleEffect(1.5)
                    .padding()
            } else if data.isEmpty {
                Text("没有数据")
                    .foregroundColor(.gray)
                    .padding()
            } else {
                DynamicTableView(
                    data: data,
                    columns: columns,
                    openEditRowWindow: { rowData in
                        openEditRowWindow(rowData: rowData)
                    },
                    deleteRow: { rowData in
                        deleteRow(rowData)
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }

            // 分页控制
            HStack {
                Button(action: {
                    if currentPage > 1 {
                        currentPage -= 1
                        pageNumberInput = "\(currentPage)"
                        fetchData()
                    }
                }) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("上一页")
                    }
                }
                .disabled(currentPage == 1)
                .buttonStyle(SecondaryButtonStyle())

                Spacer()

                Text("第 \(currentPage) 页，共 \(totalPages) 页")
                    .padding(.horizontal)

                Spacer()

                HStack {
                    Text("跳转到第")
                    TextField("页码", text: $pageNumberInput, onCommit: {
                        guard totalPages > 0 else { return }
                        if let page = Int(pageNumberInput), page >= 1 && page <= totalPages {
                            currentPage = page
                            fetchData()
                        } else {
                            alertMessage = "请输入有效的页码"
                            showAlert = true
                        }
                    })
                    .frame(width: 50)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    Text("页")
                }

                Spacer()

                Button(action: {
                    if currentPage < totalPages {
                        currentPage += 1
                        pageNumberInput = "\(currentPage)"
                        fetchData()
                    }
                }) {
                    HStack {
                        Text("下一页")
                        Image(systemName: "chevron.right")
                    }
                }
                .disabled(currentPage == totalPages || totalPages == 0)
                .buttonStyle(SecondaryButtonStyle())
            }
            .padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .navigationTitle("表：\(table)")
        .toolbar {
            ToolbarItemGroup(placement: .navigation) {
                Button(action: {
                    fetchData()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("刷新")
                    }
                }
                .buttonStyle(ToolbarButtonStyle())

                Button(action: {
                    openAddRowWindow()
                }) {
                    HStack {
                        Image(systemName: "plus.circle")
                        Text("添加行")
                    }
                }
                .buttonStyle(ToolbarButtonStyle())

                Button(action: {
                    openTableStructureWindow()
                }) {
                    HStack {
                        Image(systemName: "pencil")
                        Text("编辑表结构")
                    }
                }
                .buttonStyle(ToolbarButtonStyle())
            }
        }
        .onAppear {
            pageNumberInput = "\(currentPage)"
            fetchData()
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("错误"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }

    // 自定义按钮样式
    struct ToolbarButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            HStack {
                configuration.label
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(Color.blue.opacity(configuration.isPressed ? 0.7 : 0.5))
            .foregroundColor(.white)
            .cornerRadius(8)
        }
    }

    struct SecondaryButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            HStack {
                configuration.label
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(Color.gray.opacity(configuration.isPressed ? 0.7 : 0.5))
            .foregroundColor(.white)
            .cornerRadius(8)
        }
    }



    func fetchData() {
        isLoading = true
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.getTableData + "/\(database.name)/\(table)?page=\(currentPage)&limit=\(recordsPerPage)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            self.isLoading = false
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "加载数据失败: \(error.localizedDescription)"
                    self.showAlert = true
                    self.isLoading = false
                }
                return
            }

            guard let data = data else {
                DispatchQueue.main.async {
                    self.alertMessage = "未收到数据"
                    self.showAlert = true
                    self.isLoading = false
                }
                return
            }

            do {
                if let result = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let records = result["data"] as? [[String: Any]],
                   let total = result["totalRecords"] as? Int {
                    DispatchQueue.main.async {
                        self.data = records.map { record in
                            // 将数据转换为 [String: String]
                            var rowData: [String: String] = [:]
                            for (key, value) in record {
                                rowData[key] = "\(value)"
                            }
                            return TableRow(data: rowData)
                        }
                        
                        // 新增逻辑：优先从后端返回的 columns 字段获取列信息
                        if let columnsFromServer = result["columns"] as? [String], !columnsFromServer.isEmpty {
                            self.columns = columnsFromServer
                        } else if !records.isEmpty {
                            // 如果没有 columns 字段或其为空，则从第一条数据中获取列
                            self.columns = Array(records[0].keys)
                        } else {
                            // 如果没有数据行且没有 columns 字段，则无列信息
                            self.columns = []
                        }
                        
                        self.totalRecords = total
                        self.isLoading = false
                    }
                } else {
                    DispatchQueue.main.async {
                        self.alertMessage = "数据格式错误"
                        self.showAlert = true
                        self.isLoading = false
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = "解码数据失败: \(error.localizedDescription)"
                    self.showAlert = true
                    self.isLoading = false
                }
            }

        }.resume()
    }

    func deleteRow(_ row: [String: String]) {
        // 检查是否有唯一标识符，例如 'id' 字段
        guard let id = row["id"] else {
            self.alertMessage = "缺少ID字段，无法删除行"
            self.showAlert = true
            return
        }

        // 构建请求URL
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.deleteRow + "/\(database.name)/\(table)/\(id)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"

        // 发送网络请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "删除失败：\(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.alertMessage = "删除成功"
                self.showAlert = true
                fetchData() // 删除后刷新数据
            }
        }.resume()
    }

    // 打开添加行窗口
    func openAddRowWindow() {
        let contentView = AddRowView(database: database, table: table, host: host, port: port, columns: columns) {
            fetchData()
        }

        // 获取主屏幕的尺寸
        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 500, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "添加新行")
            return
        }

        let windowWidth = screenFrame.width * 0.3
        let windowHeight = screenFrame.height * 0.5
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "添加新行")
    }

    // 打开编辑行窗口
    func openEditRowWindow(rowData: [String: String]) {
        let contentView = EditRowView(database: database, table: table, rowData: rowData, host: host, port: port) {
            fetchData()
        }

        // 获取主屏幕的尺寸
        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 500, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "编辑行数据")
            return
        }

        let windowWidth = screenFrame.width * 0.3
        let windowHeight = screenFrame.height * 0.5
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "编辑行数据")
    }

    // 打开表结构窗口
    func openTableStructureWindow() {
        let contentView = TableStructureView(database: database, table: table, host: host, port: port)

        // 获取主屏幕的尺寸
        guard let screenFrame = NSScreen.main?.visibleFrame else {
            let defaultFrame = NSRect(x: 0, y: 0, width: 600, height: 600)
            createWindow(frame: defaultFrame, contentView: contentView, title: "表结构：\(table)")
            return
        }

        let windowWidth = screenFrame.width * 0.5
        let windowHeight = screenFrame.height * 0.6
        let windowOriginX = screenFrame.origin.x + (screenFrame.width - windowWidth) / 2
        let windowOriginY = screenFrame.origin.y + (screenFrame.height - windowHeight) / 2
        let windowFrame = NSRect(x: windowOriginX, y: windowOriginY, width: windowWidth, height: windowHeight)

        createWindow(frame: windowFrame, contentView: contentView, title: "表结构：\(table)")
    }

    // 创建窗口的通用函数
    private func createWindow<Content: View>(frame: NSRect, contentView: Content, title: String) {
        let window = NSWindow(
            contentRect: frame,
            styleMask: [.titled, .closable, .resizable, .miniaturizable],
            backing: .buffered, defer: false)
        window.title = title
        window.contentView = NSHostingView(rootView: contentView)
        window.center()

        // 确保窗口尺寸正确设置
        window.setFrame(frame, display: true)
        window.makeKeyAndOrderFront(nil)

        let windowController = NSWindowController(window: window)
        WindowManager.shared.addWindow(windowController)
    }
}

struct EditRowView: View {
    let database: Database
    let table: String
    var rowData: [String: String]
    let host: String
    let port: String  // 修改为字符串类型
    var onComplete: () -> Void

    @State private var editedData: [String: String] = [:]
    @State private var showAlert = false
    @State private var alertMessage = ""

    var body: some View {
        VStack(alignment: .leading) {
            Text("编辑行数据")
                .font(.title2)
                .bold()
                .padding(.top)

            Divider()
                .padding(.vertical, 8)

            Form {
                ForEach(rowData.keys.sorted(), id: \.self) { key in
                    HStack {
                        Text(key.capitalized)
                            .font(.headline)
                            .frame(width: 120, alignment: .leading)

                        if key.lowercased() != "id" {
                            TextField("请输入\(key)", text: Binding(
                                get: { editedData[key] ?? (rowData[key] ?? "") },
                                set: { editedData[key] = $0 }
                            ))
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .font(.body)
                        } else {
                            Text(rowData[key] ?? "")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 4)
                }
            }

            HStack {
                Spacer()
                Button("取消") {
                    closeWindow()
                }
                .buttonStyle(CancelButtonStyle())

                Button("保存") {
                    updateRow()
                }
                .buttonStyle(StartButtonStyle())
            }
            .padding(.top)

        }
        .padding()
        .frame(minWidth: 500, minHeight: 400) // 稍微加大窗口，使布局更舒适
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("提示"),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定")) {
                    if alertMessage == "更新成功" {
                        closeWindow()
                        onComplete()
                    }
                }
            )
        }
    }

    func updateRow() {
        guard let id = rowData["id"] else {
            self.alertMessage = "缺少ID字段"
            self.showAlert = true
            return
        }

        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.updateRow + "/\(database.name)/\(table)/\(id)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "PUT"

        // 合并原始数据和编辑后的数据
        var parameters = rowData
        for (key, value) in editedData {
            parameters[key] = value
        }

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters, options: [])
        } catch {
            self.alertMessage = "参数序列化失败"
            self.showAlert = true
            return
        }

        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        URLSession.shared.dataTask(with: request) { _, _, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "请求失败：\(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.alertMessage = "更新成功"
                self.showAlert = true
            }
        }.resume()
    }

    func closeWindow() {
        NSApplication.shared.keyWindow?.close()
    }
}

// 全局定义的按钮样式
struct CancelButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(configuration.isPressed ? Color.red.opacity(0.7) : Color.red)
            .foregroundColor(.white)
            .cornerRadius(8)
    }
}

struct StartButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(configuration.isPressed ? Color.green.opacity(0.7) : Color.green)
            .foregroundColor(.white)
            .cornerRadius(8)
    }
}


struct AddRowView: View {
    let database: Database
    let table: String
    let host: String
    let port: String  // 修改为字符串类型
    let columns: [String]
    var onComplete: () -> Void

    @State private var rowData: [String: String] = [:]
    @State private var showAlert = false
    @State private var alertMessage = ""

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("添加新行")
                .font(.title)
                .padding()

            ScrollView {
                VStack(spacing: 15) {
                    ForEach(columns, id: \.self) { column in
                        // 不显示 'id' 字段
                        if column.lowercased() != "id" {
                            HStack {
                                Text(column)
                                    .frame(width: 100, alignment: .leading)
                                TextField("请输入\(column)", text: Binding(
                                    get: { rowData[column] ?? "" },
                                    set: { rowData[column] = $0 }
                                ))
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                            .padding(.horizontal)
                        }
                    }
                }
            }

            Spacer()

            HStack {
                Spacer()
                Button("取消") {
                    closeWindow()
                }
                Button("保存") {
                    addRow()
                }
            }
            .padding()
        }
        .frame(minWidth: 400, minHeight: 400)
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定"), action: {
                if alertMessage == "添加成功" {
                    closeWindow()
                    onComplete()
                }
            }))
        }
    }

    func addRow() {
        guard let url = URL(string: NetworkConfig.databaseServer(host: host, port: port) + NetworkConfig.Endpoints.addRow + "/\(database.name)/\(table)") else {
            self.alertMessage = "无效的URL"
            self.showAlert = true
            return
        }

        let parameters = rowData

        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: parameters, options: [])
        } catch {
            self.alertMessage = "参数序列化失败"
            self.showAlert = true
            return
        }

        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.alertMessage = "请求失败：\(error.localizedDescription)"
                    self.showAlert = true
                }
                return
            }

            DispatchQueue.main.async {
                self.alertMessage = "添加成功"
                self.showAlert = true
            }
        }.resume()
    }

    func closeWindow() {
        NSApplication.shared.keyWindow?.close()
    }
}

class DatabaseWindowController: NSWindowController {
    convenience init(rootView: AnyView) {
        // 获取主屏幕的可见区域尺寸（不包括菜单栏和 Dock）
        let screenFrame = NSScreen.main?.visibleFrame ?? NSRect(x: 0, y: 0, width: 1024, height: 768)

        let window = NSWindow(
            contentRect: screenFrame,
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered, defer: false)

        window.title = "数据库管理"
        window.contentView = NSHostingView(rootView: rootView)
        window.isReleasedWhenClosed = false

        self.init(window: window)
    }
}
