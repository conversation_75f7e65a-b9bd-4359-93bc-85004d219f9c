//
//  finalMonitor_AI_version_1_0.swift
//  MonitorAPP
//
//  Created by 王雷 on 2024/9/9.
//

import Foundation
import SwiftUI

struct Task: Identifiable {
    let id = UUID()
    var name: String
    var urgency: Double
    var importance: Double
    var resourceNeeds: Double
    var dependency: Double
    var difficulty: Double
}

struct finalMonitor_AI_version_1_0: View {
    @State private var tasks: [Task] = []
    @State private var newTaskName: String = ""
    @State private var urgency: Double = 0
    @State private var importance: Double = 0
    @State private var resourceNeeds: Double = 0
    @State private var dependency: Double = 0
    @State private var difficulty: Double = 0
    @State private var showingResults = false
    
    var body: some View {
        VStack {
            Text("超大参数监视器计算机辅助版 - 版本 1.0")
                .font(.largeTitle)
                .padding()

            TextField("输入任务名称", text: $newTaskName)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
            
            VStack {
                HStack {
                    Text("紧急性:")
                    Slider(value: $urgency, in: 0...5, step: 1)
                    Text("\(Int(urgency))")
                }.padding()

                HStack {
                    Text("重要性:")
                    Slider(value: $importance, in: 0...5, step: 1)
                    Text("\(Int(importance))")
                }.padding()

                HStack {
                    Text("资源需求:")
                    Slider(value: $resourceNeeds, in: 0...5, step: 1)
                    Text("\(Int(resourceNeeds))")
                }.padding()

                HStack {
                    Text("依赖性:")
                    Slider(value: $dependency, in: 0...5, step: 1)
                    Text("\(Int(dependency))")
                }.padding()

                HStack {
                    Text("难度:")
                    Slider(value: $difficulty, in: 0...5, step: 1)
                    Text("\(Int(difficulty))")
                }.padding()
            }
            
            Button("添加任务") {
                let task = Task(name: newTaskName, urgency: urgency, importance: importance, resourceNeeds: resourceNeeds, dependency: dependency, difficulty: difficulty)
                tasks.append(task)
                newTaskName = ""
                urgency = 0
                importance = 0
                resourceNeeds = 0
                dependency = 0
                difficulty = 0
            }
            .padding()
            
            List(tasks) { task in
                VStack(alignment: .leading) {
                    Text("任务: \(task.name)")
                    Text("紧急性: \(Int(task.urgency)) | 重要性: \(Int(task.importance)) | 资源需求: \(Int(task.resourceNeeds)) | 依赖性: \(Int(task.dependency)) | 难度: \(Int(task.difficulty))")
                }
            }
            .padding()
            
            Button("检视打分结果") {
                showingResults = true
            }
            .padding()
            .sheet(isPresented: $showingResults) {
                ReviewResultsView(tasks: tasks)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.gray.opacity(0.1))
    }
}//finalMonitor_AI_version_1_0 view代码结束

struct ReviewResultsView: View {
    var tasks: [Task]
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack(alignment: .leading) {
            Text("回顾打分标准")
                .font(.headline)
                .padding(.top)
            Text("1. 紧急性：任务是否有明确的截止日期？")
            Text("2. 重要性：任务对长期目标的影响如何？")
            Text("3. 资源需求：是否有足够的资源完成任务？")
            Text("4. 依赖性：是否依赖其他任务？")
            Text("5. 难度：任务的复杂程度？")
            
            Divider()
                .padding(.vertical)
            
            Text("情景分析法")
                .font(.headline)
                .padding(.top)
            Text("如果不做某个任务，是否会产生严重后果？")
            
            Divider()
                .padding(.vertical)
            
            Text("拆解问题")
                .font(.headline)
                .padding(.top)
            Text("是否可以将复杂任务拆解为更小的任务并单独评分？")
            
            Divider()
                .padding(.vertical)
            
            Text("与他人讨论")
                .font(.headline)
                .padding(.top)
            Text("通过与他人讨论，获得新的视角来判断任务的优先级。")
            
            Divider()
                .padding(.vertical)
            
            Text("逐步调整")
                .font(.headline)
                .padding(.top)
            Text("如果完成一个任务后感觉不合适，是否需要重新调整优先级？")
            
            Spacer()
            
            Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            }
            .padding()
        }
        .padding()
    }
}//ReviewResultsView代码结束


class FinalMonitorWindowController: NSWindowController, NSWindowDelegate { // 添加 NSWindowDelegate
    
    convenience init(rootView: NSView) {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false)
        window.center()
        window.setFrameAutosaveName("AI Monitor Window")
        window.contentView = rootView
        window.makeKeyAndOrderFront(nil)
        window.isReleasedWhenClosed = false
        
        self.init(window: window)
        
        // 必须在调用 self.init 之后设置代理
        self.window?.delegate = self
    }
    
    func showWindowAnimated() {
        guard let window = self.window else { return }
        
        // 显示窗口
        window.makeKeyAndOrderFront(nil)
        
        // 获取屏幕尺寸
        guard let screen = window.screen else { return }
        let screenFrame = screen.frame
        
        // 计算目标矩形，使其全屏
        let targetRect = NSRect(
            x: 0,
            y: 0,
            width: screenFrame.width,
            height: screenFrame.height
        )
        
        // 使用动画来调整窗口的大小和位置
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.5 // 动画持续时间
            window.animator().setFrame(targetRect, display: true, animate: true)
        }
    }

    // MARK: - NSWindowDelegate
    func windowWillClose(_ notification: Notification) {
        // 发布通知，告知主视图系统窗口已关闭
        NotificationCenter.default.post(name: .didCloseSystemWindow, object: nil)
    }
}//class代码结束
