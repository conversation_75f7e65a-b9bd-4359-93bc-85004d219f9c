import Foundation

struct NetworkConfig {
    // 认证配置
    static let authToken = "13795375098"  // 从config.py中获取的AUTH_KEY
    
    // 主服务器配置
    static let mainServer = "http://************:5100"
    
    // 监控服务器配置
    static let monitorServerForScanServer = "http://************:5301"  // 用于 ScanServer.swift
    static let monitorServerForD3test = "http://************:5301"      // 用于 D3test.swift
    static let monitorServerForScanServer2 = "http://************:5301" // 用于 ScanServer2.swift
    
    // 文件服务器配置
    static let fileServer = "http://************:5100"
    static let fileServerIP = "************"
    static let fileServerPort = "5100"
    

    // 数据库服务器配置
    struct Database {
        static let defaultHost = "************"
        static let defaultPort = "5200"
    }
    
    // OpenAI配置
    struct OpenAI {
        static let apiServer = "https://api.openai.com/v1"
        static let apiKey = "YOUR_API_KEY_HERE" // 请替换为实际的API Key
        
        struct Endpoints {
            static let chatCompletions = "/chat/completions"
        }
    }
    
    // 数据库服务器配置
    static func databaseServer(host: String, port: String) -> String {
        return "http://\(host):\(port)"
    }
    
    // API端点
    struct Endpoints {
        // 日历相关
        static let getPendingTasks = "/get_pending_tasks"
        static let getTotalTaskCount = "/get_total_task_count"
        static let completeTask = "/complete_task"
        static let deleteTask = "/delete_task"
        static let searchTasks = "/search_tasks"
        static let addThought = "/addThought"
        
        // 联系人相关
        static let uploadContacts = "/uploadContacts"
        
        // 监控相关
        static let status = "/status"
        static let monitor = "/monitor"
        static let systems = "/systems"
        static let nodes = "/nodes"
        static let nodeTypes = "/node_types"
        static let viewControlParams = "/view_control_params"
        static let richTextNodes = "/rich_text_nodes"
        static let connections = "/connections"
        
        // 文件操作相关
        static let readFile = "/files/read"
        static let writeFile = "/files/write"
        static let execute = "/execute"
        static let createFile = "/files/create"
        static let renameFile = "/files/rename"
        static let deleteFile = "/files/delete"
        static let backupFile = "/files/backup"
        static let downloadFiles = "/files/download"
        static let checkPorts = "/check_ports"
        static let routesInfo = "/routes_info"
        static let getFiles = "/getFiles"
        static let uploadFile = "/uploadFile"
        static let downloadFile = "/downloadFile"
        static let saveSlotFileMapping = "/saveSlotFileMapping"
        static let loadSlotFileMappings = "/loadSlotFileMappings"
        
        // 数据库相关 (修正以匹配 database_manager.py)
        static let getDatabases = "/getDatabases"
        static let getTables = "/getTables"
        static let deleteTable = "/deleteTable"
        static let downloadBackup = "/downloadBackup"
        static let createTable = "/createTable"
        static let getTableSchema = "/getTableSchema"
        static let alterTable = "/alterTable"
        static let getTableData = "/getTableData"
        static let deleteRow = "/deleteRow"
        static let updateRow = "/updateRow"
        static let addRow = "/addRow"
    }
}

extension Notification.Name {
    static let didCloseSystemWindow = Notification.Name("didCloseSystemWindow")
}
