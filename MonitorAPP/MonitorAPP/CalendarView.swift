//
//  Calendar.swift
//  MonitorAPP
//
//  Created by 王雷 on 2024/9/27.
//

import SwiftUI
import AppKit

struct TaskItem: Codable {
    var task: String
    var dateAdded: String

    enum CodingKeys: String, CodingKey {
        case task
        case dateAdded = "date_added"
    }
}



struct CalendarView: View {
    @State private var thought: String = ""
    @State private var statusMessage: String = ""
    @State private var showAlert = false

    var body: some View {
        VStack(alignment: .center) {
            // 标题保持居中
            Text("日常思绪或任务")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.primary)
                .padding(.top, 40)
                .padding(.bottom, 20)

            // 这里将 HStack 调整为左对齐
            HStack(alignment: .top) {
                Image(systemName: "pencil.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
                VStack(alignment: .leading) { // 确保文本和输入框左对齐
                    Text("请输入:")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    
                    TextEditor(text: $thought)
                        .font(.system(size: 18))
                        .padding(10)
                        .background(Color(NSColor.windowBackgroundColor))
                        .cornerRadius(10)
                        .frame(height: 200)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.gray, lineWidth: 1)
                        )
                }
            }

            Button(action: {
                let task = TaskItem(task: self.thought, dateAdded: self.currentDate())
                self.saveTaskToDatabase(task) { success in
                    DispatchQueue.main.async {
                        if success {
                            self.statusMessage = "想法已保存！"
                            self.thought = "" // 清空输入框
                        } else {
                            self.statusMessage = "保存失败，请重试。"
                        }
                        self.showAlert = true
                    }
                }
            }) {
                Text("保存")
                    .font(.headline)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
            .padding(.top, 20)

            Spacer()
        }
        .padding(20)
        .frame(width: 500, height: 500)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(radius: 10)
        .alert(isPresented: $showAlert) {
            Alert(title: Text(statusMessage))
        }
    }
    
    func currentDate() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: Date())
    }
    
    func saveTaskToDatabase(_ task: TaskItem, completion: @escaping (Bool) -> Void) {
        guard let url = URL(string: NetworkConfig.mainServer + NetworkConfig.Endpoints.uploadContacts) else {
            completion(false)
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        guard let httpBody = try? JSONEncoder().encode(task) else {
            completion(false)
            return
        }
        request.httpBody = httpBody

        print("\n--- Preparing Request (saveTaskToDatabase/uploadContacts) ---")
        print("URL: \(url.absoluteString)")
        print("Method: \(request.httpMethod ?? "nil")")
        print("Headers: \(request.allHTTPHeaderFields ?? [:])")
        if let bodyData = request.httpBody, let bodyString = String(data: bodyData, encoding: .utf8) {
            print("Body: \(bodyString)")
        } else {
            print("Body: nil or encoding error")
        }
        print("--- Sending Request ---")
        
        URLSession.shared.dataTask(with: request) { _, response, error in
            if let error = error {
                print("Error: \(error.localizedDescription)")
                completion(false)
            } else if let httpResponse = response as? HTTPURLResponse {
                print("Status code: \(httpResponse.statusCode)")
                completion(httpResponse.statusCode == 200)
            } else {
                completion(false)
            }
        }.resume()
    }
}




import SwiftUI
import AppKit

class FinalMonitorWindowController1: NSWindowController, NSWindowDelegate { // 添加 NSWindowDelegate
    
    convenience init(rootView: NSView) {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false)
        window.center() // 先设置为中心
        window.setFrameAutosaveName("AI Monitor Window")
        window.contentView = rootView
        window.makeKeyAndOrderFront(nil)
        window.isReleasedWhenClosed = false
        
        self.init(window: window)
        
        // 必须在 self.init 之后设置代理和添加监听器
        self.window?.delegate = self // 设置代理
        // 添加监听器，当窗口显示时，调用居中方法
        NotificationCenter.default.addObserver(self, selector: #selector(windowDidResize), name: NSWindow.didResizeNotification, object: self.window)
    }
    
    @objc func windowDidResize() {
        centerWindow()
    }
    
    func centerWindow() {
        if let screenFrame = self.window?.screen?.frame, let windowFrame = self.window?.frame {
            // 计算新的窗口位置，使其居中
            let x = (screenFrame.width - windowFrame.width) / 2
            let y = (screenFrame.height - windowFrame.height) / 2
            self.window?.setFrameOrigin(NSPoint(x: x, y: y))
        }
    }
    
    func showWindowAnimated() {
        guard let window = self.window else { return }
        
        // 显示窗口并居中
        window.makeKeyAndOrderFront(nil)
        centerWindow()
    }

    // MARK: - NSWindowDelegate
    func windowWillClose(_ notification: Notification) {
        // 发布通知，告知主视图系统窗口已关闭
        NotificationCenter.default.post(name: .didCloseSystemWindow, object: nil)
    }
}
